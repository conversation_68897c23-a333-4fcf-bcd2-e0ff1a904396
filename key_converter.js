#!/usr/bin/env node
/**
 * Key Converter Tool
 * أداة تحويل مفاتيح DRM بين التنسيقات المختلفة
 */

class KeyConverter {
    /**
     * تحويل Base64 إلى Hex
     */
    static base64ToHex(base64) {
        try {
            const buffer = Buffer.from(base64, 'base64');
            return buffer.toString('hex').toLowerCase();
        } catch (error) {
            throw new Error(`خطأ في تحويل Base64 إلى Hex: ${error.message}`);
        }
    }

    /**
     * تحويل Hex إلى Base64
     */
    static hexToBase64(hex) {
        try {
            const buffer = Buffer.from(hex, 'hex');
            return buffer.toString('base64');
        } catch (error) {
            throw new Error(`خطأ في تحويل Hex إلى Base64: ${error.message}`);
        }
    }

    /**
     * تحويل Key ID إلى تنسيق UUID
     */
    static keyIdToUUID(keyId) {
        try {
            let hex;
            if (keyId.includes('=')) {
                // Base64 format
                hex = this.base64ToHex(keyId);
            } else {
                // Hex format
                hex = keyId.replace(/[^a-fA-F0-9]/g, '').toLowerCase();
            }

            if (hex.length !== 32) {
                throw new Error('Key ID يجب أن يكون 16 بايت (32 حرف hex)');
            }

            // تنسيق UUID: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
            return `${hex.slice(0, 8)}-${hex.slice(8, 12)}-${hex.slice(12, 16)}-${hex.slice(16, 20)}-${hex.slice(20, 32)}`;
        } catch (error) {
            throw new Error(`خطأ في تحويل إلى UUID: ${error.message}`);
        }
    }

    /**
     * إزالة الشرطات من UUID
     */
    static uuidToKeyId(uuid) {
        try {
            const hex = uuid.replace(/-/g, '').toLowerCase();
            if (hex.length !== 32) {
                throw new Error('UUID غير صحيح');
            }
            return hex;
        } catch (error) {
            throw new Error(`خطأ في تحويل UUID: ${error.message}`);
        }
    }

    /**
     * تحويل Key ID لتنسيق Widevine
     */
    static toWidevineFormat(keyId) {
        try {
            const hex = keyId.includes('=') ? this.base64ToHex(keyId) : keyId.replace(/[^a-fA-F0-9]/g, '');
            return hex.toLowerCase();
        } catch (error) {
            throw new Error(`خطأ في تحويل لتنسيق Widevine: ${error.message}`);
        }
    }

    /**
     * تحليل شامل للمفتاح
     */
    static analyzeKey(key) {
        const result = {
            original: key,
            formats: {}
        };

        try {
            // تحديد نوع المفتاح
            if (key.includes('=')) {
                result.type = 'Base64';
                result.formats.hex = this.base64ToHex(key);
                result.formats.base64 = key;
            } else if (key.includes('-')) {
                result.type = 'UUID';
                result.formats.hex = this.uuidToKeyId(key);
                result.formats.base64 = this.hexToBase64(result.formats.hex);
                result.formats.uuid = key;
            } else {
                result.type = 'Hex';
                result.formats.hex = key.toLowerCase();
                result.formats.base64 = this.hexToBase64(key);
            }

            // إضافة تنسيقات إضافية
            if (!result.formats.uuid) {
                result.formats.uuid = this.keyIdToUUID(result.formats.base64);
            }
            
            result.formats.widevine = this.toWidevineFormat(key);
            result.formats.length = Buffer.from(result.formats.hex, 'hex').length;

        } catch (error) {
            result.error = error.message;
        }

        return result;
    }

    /**
     * طباعة تحليل المفتاح
     */
    static printKeyAnalysis(key) {
        console.log(`\n🔑 تحليل المفتاح: ${key}`);
        console.log("=".repeat(50));

        const analysis = this.analyzeKey(key);

        if (analysis.error) {
            console.log(`❌ خطأ: ${analysis.error}`);
            return;
        }

        console.log(`📝 النوع: ${analysis.type}`);
        console.log(`📏 الطول: ${analysis.formats.length} بايت`);
        console.log(`\n📋 التنسيقات:`);
        console.log(`   Base64:   ${analysis.formats.base64}`);
        console.log(`   Hex:      ${analysis.formats.hex}`);
        console.log(`   UUID:     ${analysis.formats.uuid}`);
        console.log(`   Widevine: ${analysis.formats.widevine}`);
    }
}

// الوظيفة الرئيسية
function main() {
    console.log("🔧 أداة تحويل مفاتيح DRM");
    console.log("=".repeat(40));

    // المفاتيح المستخرجة من PSSH
    const keys = [
        "/r6HHHvilE2YQi+D/+BbCg==",  // Key ID من PlayReady
        "VlR7IdsIJEuRd06Laqs2jw=="   // DS_ID من PlayReady
    ];

    keys.forEach(key => {
        KeyConverter.printKeyAnalysis(key);
    });

    // مثال على تحويل إضافي
    console.log("\n" + "=".repeat(60));
    console.log("💡 معلومات إضافية:");
    console.log("=".repeat(60));
    
    console.log("🎯 Key ID الرئيسي: /r6HHHvilE2YQi+D/+BbCg==");
    console.log("🌐 License Server: https://playready.keyos.com/api/v4/getLicense");
    console.log("🔐 Algorithm: AES-CTR");
    console.log("📏 Key Length: 16 bytes (128-bit)");
    console.log("🏷️ DRM Systems: PlayReady + Widevine");
}

// تشغيل الأداة
if (require.main === module) {
    main();
}

module.exports = KeyConverter;
