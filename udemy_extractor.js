#!/usr/bin/env node
/**
 * Udemy Video Extractor
 * أداة استخراج معلومات الفيديو من صفحات Udemy
 */

const https = require('https');
const fs = require('fs');

class UdemyExtractor {
    constructor() {
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        };
    }

    /**
     * جلب محتوى صفحة Udemy
     */
    async fetchPage(url) {
        return new Promise((resolve, reject) => {
            console.log(`🌐 جاري جلب الصفحة: ${url}`);
            
            const request = https.get(url, { headers: this.headers }, (response) => {
                let data = '';
                
                response.on('data', (chunk) => {
                    data += chunk;
                });
                
                response.on('end', () => {
                    console.log(`✅ تم جلب الصفحة بنجاح (${data.length} حرف)`);
                    resolve(data);
                });
            });
            
            request.on('error', (error) => {
                console.error(`❌ خطأ في جلب الصفحة: ${error.message}`);
                reject(error);
            });
            
            request.setTimeout(30000, () => {
                request.destroy();
                reject(new Error('انتهت مهلة الطلب'));
            });
        });
    }

    /**
     * استخراج معلومات الفيديو من HTML
     */
    extractVideoInfo(html) {
        console.log('🔍 جاري البحث عن معلومات الفيديو...');
        
        const results = {
            course_id: null,
            lecture_id: null,
            video_sources: [],
            mpd_urls: [],
            api_endpoints: [],
            tokens: [],
            drm_info: {}
        };

        try {
            // استخراج Course ID
            const courseIdMatch = html.match(/course[_-]?id['":\s]*(\d+)/i);
            if (courseIdMatch) {
                results.course_id = courseIdMatch[1];
                console.log(`📚 Course ID: ${results.course_id}`);
            }

            // استخراج Lecture ID
            const lectureIdMatch = html.match(/lecture[_-]?id['":\s]*(\d+)/i);
            if (lectureIdMatch) {
                results.lecture_id = lectureIdMatch[1];
                console.log(`🎥 Lecture ID: ${results.lecture_id}`);
            }

            // البحث عن روابط MPD
            const mpdMatches = html.match(/https?:\/\/[^"'\s]+\.mpd[^"'\s]*/gi);
            if (mpdMatches) {
                results.mpd_urls = [...new Set(mpdMatches)];
                console.log(`🎬 تم العثور على ${results.mpd_urls.length} رابط MPD`);
                results.mpd_urls.forEach((url, i) => {
                    console.log(`   ${i + 1}. ${url.substring(0, 100)}...`);
                });
            }

            // البحث عن API endpoints
            const apiMatches = html.match(/https?:\/\/[^"'\s]*udemy[^"'\s]*\/api[^"'\s]*/gi);
            if (apiMatches) {
                results.api_endpoints = [...new Set(apiMatches)];
                console.log(`🔗 تم العثور على ${results.api_endpoints.length} API endpoint`);
            }

            // البحث عن Tokens
            const tokenMatches = html.match(/['"](eyJ[A-Za-z0-9+/=]+\.[A-Za-z0-9+/=]+\.[A-Za-z0-9+/=_-]+)['"]/g);
            if (tokenMatches) {
                results.tokens = [...new Set(tokenMatches.map(t => t.replace(/['"]/g, '')))];
                console.log(`🎫 تم العثور على ${results.tokens.length} token`);
            }

            // البحث عن معلومات DRM
            const drmMatches = html.match(/widevine|playready|drm|cenc/gi);
            if (drmMatches) {
                results.drm_info.found = true;
                results.drm_info.types = [...new Set(drmMatches.map(m => m.toLowerCase()))];
                console.log(`🔐 تم العثور على DRM: ${results.drm_info.types.join(', ')}`);
            }

            // البحث عن video sources في JavaScript
            const videoSourceRegex = /"sources":\s*\[([^\]]+)\]/g;
            let match;
            while ((match = videoSourceRegex.exec(html)) !== null) {
                try {
                    const sourcesText = match[1];
                    console.log(`📹 تم العثور على مصادر فيديو: ${sourcesText.substring(0, 200)}...`);
                    results.video_sources.push(sourcesText);
                } catch (e) {
                    console.log(`⚠️ خطأ في تحليل مصادر الفيديو: ${e.message}`);
                }
            }

        } catch (error) {
            console.error(`❌ خطأ في استخراج المعلومات: ${error.message}`);
        }

        return results;
    }

    /**
     * فك تشفير JWT Token
     */
    decodeJWT(token) {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) return null;

            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
            return payload;
        } catch (error) {
            return null;
        }
    }

    /**
     * تحليل Tokens المستخرجة
     */
    analyzeTokens(tokens) {
        console.log('\n🎫 تحليل Tokens:');
        console.log('='.repeat(50));

        tokens.forEach((token, i) => {
            console.log(`\nToken ${i + 1}:`);
            console.log(`Raw: ${token.substring(0, 50)}...`);
            
            const decoded = this.decodeJWT(token);
            if (decoded) {
                console.log('Decoded payload:');
                Object.keys(decoded).forEach(key => {
                    console.log(`  ${key}: ${JSON.stringify(decoded[key])}`);
                });
            } else {
                console.log('  ❌ فشل في فك التشفير');
            }
        });
    }

    /**
     * حفظ النتائج في ملف
     */
    saveResults(results, filename = 'udemy_extraction_results.json') {
        try {
            fs.writeFileSync(filename, JSON.stringify(results, null, 2));
            console.log(`💾 تم حفظ النتائج في: ${filename}`);
        } catch (error) {
            console.error(`❌ خطأ في حفظ النتائج: ${error.message}`);
        }
    }

    /**
     * طباعة النتائج
     */
    printResults(results) {
        console.log('\n' + '='.repeat(60));
        console.log('📊 نتائج الاستخراج');
        console.log('='.repeat(60));

        console.log(`📚 Course ID: ${results.course_id || 'غير موجود'}`);
        console.log(`🎥 Lecture ID: ${results.lecture_id || 'غير موجود'}`);
        
        if (results.mpd_urls.length > 0) {
            console.log(`\n🎬 روابط MPD (${results.mpd_urls.length}):`);
            results.mpd_urls.forEach((url, i) => {
                console.log(`  ${i + 1}. ${url}`);
            });
        }

        if (results.api_endpoints.length > 0) {
            console.log(`\n🔗 API Endpoints (${results.api_endpoints.length}):`);
            results.api_endpoints.slice(0, 5).forEach((url, i) => {
                console.log(`  ${i + 1}. ${url}`);
            });
            if (results.api_endpoints.length > 5) {
                console.log(`  ... و ${results.api_endpoints.length - 5} أخرى`);
            }
        }

        if (results.tokens.length > 0) {
            console.log(`\n🎫 Tokens (${results.tokens.length}):`);
            this.analyzeTokens(results.tokens.slice(0, 3));
        }

        if (results.drm_info.found) {
            console.log(`\n🔐 معلومات DRM:`);
            console.log(`  الأنواع: ${results.drm_info.types.join(', ')}`);
        }
    }

    /**
     * الوظيفة الرئيسية للاستخراج
     */
    async extract(url) {
        try {
            console.log('🚀 بدء عملية الاستخراج...');
            
            // جلب الصفحة
            const html = await this.fetchPage(url);
            
            // استخراج المعلومات
            const results = this.extractVideoInfo(html);
            
            // طباعة النتائج
            this.printResults(results);
            
            // حفظ النتائج
            this.saveResults(results);
            
            return results;
            
        } catch (error) {
            console.error(`❌ خطأ في عملية الاستخراج: ${error.message}`);
            return null;
        }
    }
}

// الوظيفة الرئيسية
async function main() {
    console.log('🎯 أداة استخراج معلومات فيديو Udemy');
    console.log('='.repeat(50));

    const url = 'https://www.udemy.com/course/computermain/learn/lecture/30801830#overview';
    
    const extractor = new UdemyExtractor();
    const results = await extractor.extract(url);
    
    if (results && results.mpd_urls.length > 0) {
        console.log('\n💡 الخطوات التالية:');
        console.log('1. استخدم رابط MPD مع N_m3u8DL-RE');
        console.log('2. احصل على مفاتيح DRM من المتصفح');
        console.log('3. استخدم mp4decrypt لفك التشفير');
    }
}

// تشغيل الأداة
if (require.main === module) {
    main().catch(console.error);
}

module.exports = UdemyExtractor;
