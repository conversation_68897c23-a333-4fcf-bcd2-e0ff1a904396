<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔑 مستخرج مفاتيح DRM من المتصفح</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #007bff;
        }
        
        .step h3 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
        
        .input-group {
            margin: 20px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }
        
        .input-group textarea {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            direction: ltr;
            text-align: left;
        }
        
        .input-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }
        
        .btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .result.show {
            display: block;
        }
        
        .result h4 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .key-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            word-break: break-all;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        
        .warning strong {
            color: #d63031;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .instructions h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .instructions ol {
            padding-right: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 مستخرج مفاتيح DRM</h1>
            <p>أداة لاستخراج مفاتيح فك التشفير من طلبات المتصفح</p>
        </div>
        
        <div class="content">
            <div class="step">
                <h3><span class="step-number">1</span>تحضير المتصفح</h3>
                <div class="instructions">
                    <h4>📋 الخطوات:</h4>
                    <ol>
                        <li>افتح Chrome أو Edge</li>
                        <li>اذهب لصفحة الفيديو في Udemy</li>
                        <li>اضغط F12 لفتح Developer Tools</li>
                        <li>اذهب لتبويب "Network"</li>
                        <li>فعّل "Preserve log" ✅</li>
                        <li>امسح الشبكة (Clear) 🗑️</li>
                    </ol>
                </div>
            </div>
            
            <div class="step">
                <h3><span class="step-number">2</span>تسجيل طلبات الشبكة</h3>
                <div class="instructions">
                    <h4>🎬 ابدأ التسجيل:</h4>
                    <ol>
                        <li>شغّل الفيديو في Udemy</li>
                        <li>انتظر حتى يبدأ التشغيل</li>
                        <li>ابحث عن الطلبات التالية في Network:</li>
                    </ol>
                </div>
                <div class="code-block">
POST https://playready.keyos.com/api/v4/getLicense
POST https://www.udemy.com/api-2.0/ecl
GET https://www.udemy.com/assets/.../index.mpd
                </div>
            </div>
            
            <div class="step">
                <h3><span class="step-number">3</span>نسخ بيانات الطلب</h3>
                <div class="input-group">
                    <label>📋 انسخ Request Payload من طلب License:</label>
                    <textarea id="requestPayload" placeholder="الصق هنا Request Payload من طلب getLicense..."></textarea>
                </div>
                
                <div class="input-group">
                    <label>📋 انسخ Response من طلب License:</label>
                    <textarea id="responsePayload" placeholder="الصق هنا Response من طلب getLicense..."></textarea>
                </div>
                
                <button class="btn" onclick="extractKeys()">🔍 استخراج المفاتيح</button>
                <button class="btn btn-secondary" onclick="clearAll()">🗑️ مسح الكل</button>
            </div>
            
            <div class="result" id="result">
                <h4>🎉 النتائج المستخرجة:</h4>
                <div id="extractedKeys"></div>
            </div>
            
            <div class="warning">
                <strong>⚠️ تحذير:</strong> هذه الأداة للأغراض التعليمية فقط. تأكد من احترام حقوق الطبع والنشر وشروط الخدمة.
            </div>
            
            <div class="step">
                <h3><span class="step-number">4</span>استخدام المفاتيح</h3>
                <div class="code-block" id="finalCommand">
N_m3u8DL-RE.exe "your_mpd_url" --key "KID:KEY" --save-name "video_name"
                </div>
            </div>
        </div>
    </div>

    <script>
        function extractKeys() {
            const requestPayload = document.getElementById('requestPayload').value;
            const responsePayload = document.getElementById('responsePayload').value;
            const resultDiv = document.getElementById('result');
            const keysDiv = document.getElementById('extractedKeys');
            
            if (!requestPayload && !responsePayload) {
                alert('يرجى إدخال بيانات الطلب أو الاستجابة');
                return;
            }
            
            let extractedData = [];
            
            // استخراج PSSH من Request
            if (requestPayload) {
                try {
                    // البحث عن PSSH في Base64
                    const psshMatches = requestPayload.match(/[A-Za-z0-9+/]{100,}={0,2}/g);
                    if (psshMatches) {
                        psshMatches.forEach((pssh, index) => {
                            if (pssh.length > 100) {
                                extractedData.push({
                                    type: 'PSSH',
                                    data: pssh,
                                    description: `PSSH ${index + 1} من Request`
                                });
                            }
                        });
                    }
                } catch (e) {
                    console.error('خطأ في استخراج PSSH:', e);
                }
            }
            
            // استخراج مفاتيح من Response
            if (responsePayload) {
                try {
                    // البحث عن مفاتيح في تنسيقات مختلفة
                    const keyPatterns = [
                        /[a-fA-F0-9]{32}:[a-fA-F0-9]{32}/g, // KID:KEY format
                        /[a-fA-F0-9]{32}/g, // Hex keys
                        /[A-Za-z0-9+/]{22}==/g // Base64 keys
                    ];
                    
                    keyPatterns.forEach((pattern, patternIndex) => {
                        const matches = responsePayload.match(pattern);
                        if (matches) {
                            matches.forEach((match, index) => {
                                if (match.length >= 22) {
                                    extractedData.push({
                                        type: patternIndex === 0 ? 'KEY_PAIR' : 'KEY',
                                        data: match,
                                        description: `مفتاح ${index + 1} من Response`
                                    });
                                }
                            });
                        }
                    });
                } catch (e) {
                    console.error('خطأ في استخراج المفاتيح:', e);
                }
            }
            
            // عرض النتائج
            if (extractedData.length > 0) {
                let html = '';
                extractedData.forEach((item, index) => {
                    html += `
                        <div class="key-item">
                            <button class="copy-btn" onclick="copyToClipboard('${item.data}')">📋 نسخ</button>
                            <strong>${item.description}:</strong><br>
                            <code>${item.data}</code>
                        </div>
                    `;
                });
                
                keysDiv.innerHTML = html;
                resultDiv.classList.add('show');
                
                // تحديث الأمر النهائي
                if (extractedData.some(item => item.type === 'KEY_PAIR')) {
                    const keyPair = extractedData.find(item => item.type === 'KEY_PAIR');
                    document.getElementById('finalCommand').textContent = 
                        `N_m3u8DL-RE.exe "your_mpd_url" --key "${keyPair.data}" --save-name "video_name"`;
                }
            } else {
                keysDiv.innerHTML = '<div class="key-item">❌ لم يتم العثور على مفاتيح. تأكد من صحة البيانات المدخلة.</div>';
                resultDiv.classList.add('show');
            }
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('تم نسخ النص إلى الحافظة! 📋');
            }).catch(err => {
                console.error('خطأ في النسخ:', err);
                // Fallback للمتصفحات القديمة
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ النص إلى الحافظة! 📋');
            });
        }
        
        function clearAll() {
            document.getElementById('requestPayload').value = '';
            document.getElementById('responsePayload').value = '';
            document.getElementById('result').classList.remove('show');
        }
        
        // إضافة تلميحات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('paste', function() {
                    setTimeout(() => {
                        if (this.value.length > 50) {
                            this.style.borderColor = '#28a745';
                        }
                    }, 100);
                });
            });
        });
    </script>
</body>
</html>
