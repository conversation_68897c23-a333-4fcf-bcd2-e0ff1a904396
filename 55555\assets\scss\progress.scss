.progress-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    right: 30px;
    top: 150px;
    z-index: 9999999;
    cursor: pointer;
}

.progress-container-sf{
  animation: scaleAnimation 2s infinite ease-in-out;
}

.progress-circle {
  width: 60px;
  height: 60px;
  background: conic-gradient(#7A93FF 0% 0%, #e0e0e0 0% 100%);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 50px;
  height: 50px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  z-index: 99999999;
}

.progress-text {
  font-size: 14px;
  color: #333;
  z-index: 199999999;
}

@keyframes scaleAnimation {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1);
    }
}