(() => {
  var e = {
      729: (e) => {
        "use strict";
        var t = Object.prototype.hasOwnProperty,
          n = "~";
        function r() {}
        function o(e, t, n) {
          ((this.fn = e), (this.context = t), (this.once = n || !1));
        }
        function s(e, t, r, s, i) {
          if ("function" != typeof r)
            throw new TypeError("The listener must be a function");
          var a = new o(r, s || e, i),
            d = n ? n + t : t;
          return (
            e._events[d]
              ? e._events[d].fn
                ? (e._events[d] = [e._events[d], a])
                : e._events[d].push(a)
              : ((e._events[d] = a), e._eventsCount++),
            e
          );
        }
        function i(e, t) {
          0 == --e._eventsCount ? (e._events = new r()) : delete e._events[t];
        }
        function a() {
          ((this._events = new r()), (this._eventsCount = 0));
        }
        (Object.create &&
          ((r.prototype = Object.create(null)), new r().__proto__ || (n = !1)),
          (a.prototype.eventNames = function () {
            var e,
              r,
              o = [];
            if (0 === this._eventsCount) return o;
            for (r in (e = this._events))
              t.call(e, r) && o.push(n ? r.slice(1) : r);
            return Object.getOwnPropertySymbols
              ? o.concat(Object.getOwnPropertySymbols(e))
              : o;
          }),
          (a.prototype.listeners = function (e) {
            var t = n ? n + e : e,
              r = this._events[t];
            if (!r) return [];
            if (r.fn) return [r.fn];
            for (var o = 0, s = r.length, i = new Array(s); o < s; o++)
              i[o] = r[o].fn;
            return i;
          }),
          (a.prototype.listenerCount = function (e) {
            var t = n ? n + e : e,
              r = this._events[t];
            return r ? (r.fn ? 1 : r.length) : 0;
          }),
          (a.prototype.emit = function (e, t, r, o, s, i) {
            var a = n ? n + e : e;
            if (!this._events[a]) return !1;
            var d,
              l,
              c = this._events[a],
              u = arguments.length;
            if (c.fn) {
              switch ((c.once && this.removeListener(e, c.fn, void 0, !0), u)) {
                case 1:
                  return (c.fn.call(c.context), !0);
                case 2:
                  return (c.fn.call(c.context, t), !0);
                case 3:
                  return (c.fn.call(c.context, t, r), !0);
                case 4:
                  return (c.fn.call(c.context, t, r, o), !0);
                case 5:
                  return (c.fn.call(c.context, t, r, o, s), !0);
                case 6:
                  return (c.fn.call(c.context, t, r, o, s, i), !0);
              }
              for (l = 1, d = new Array(u - 1); l < u; l++)
                d[l - 1] = arguments[l];
              c.fn.apply(c.context, d);
            } else {
              var g,
                m = c.length;
              for (l = 0; l < m; l++)
                switch (
                  (c[l].once && this.removeListener(e, c[l].fn, void 0, !0), u)
                ) {
                  case 1:
                    c[l].fn.call(c[l].context);
                    break;
                  case 2:
                    c[l].fn.call(c[l].context, t);
                    break;
                  case 3:
                    c[l].fn.call(c[l].context, t, r);
                    break;
                  case 4:
                    c[l].fn.call(c[l].context, t, r, o);
                    break;
                  default:
                    if (!d)
                      for (g = 1, d = new Array(u - 1); g < u; g++)
                        d[g - 1] = arguments[g];
                    c[l].fn.apply(c[l].context, d);
                }
            }
            return !0;
          }),
          (a.prototype.on = function (e, t, n) {
            return s(this, e, t, n, !1);
          }),
          (a.prototype.once = function (e, t, n) {
            return s(this, e, t, n, !0);
          }),
          (a.prototype.removeListener = function (e, t, r, o) {
            var s = n ? n + e : e;
            if (!this._events[s]) return this;
            if (!t) return (i(this, s), this);
            var a = this._events[s];
            if (a.fn)
              a.fn !== t ||
                (o && !a.once) ||
                (r && a.context !== r) ||
                i(this, s);
            else {
              for (var d = 0, l = [], c = a.length; d < c; d++)
                (a[d].fn !== t ||
                  (o && !a[d].once) ||
                  (r && a[d].context !== r)) &&
                  l.push(a[d]);
              l.length
                ? (this._events[s] = 1 === l.length ? l[0] : l)
                : i(this, s);
            }
            return this;
          }),
          (a.prototype.removeAllListeners = function (e) {
            var t;
            return (
              e
                ? ((t = n ? n + e : e), this._events[t] && i(this, t))
                : ((this._events = new r()), (this._eventsCount = 0)),
              this
            );
          }),
          (a.prototype.off = a.prototype.removeListener),
          (a.prototype.addListener = a.prototype.on),
          (a.prefixed = n),
          (a.EventEmitter = a),
          (e.exports = a));
      },
      345: (e) => {
        "use strict";
        e.exports = (e, t) => (
          (t = t || (() => {})),
          e.then(
            (e) =>
              new Promise((e) => {
                e(t());
              }).then(() => e),
            (e) =>
              new Promise((e) => {
                e(t());
              }).then(() => {
                throw e;
              }),
          )
        );
      },
      860: (e, t, n) => {
        "use strict";
        const r = n(729),
          o = n(512),
          s = n(506),
          i = () => {},
          a = new o.TimeoutError();
        t.Z = class extends r {
          constructor(e) {
            var t, n, r, o;
            if (
              (super(),
              (this._intervalCount = 0),
              (this._intervalEnd = 0),
              (this._pendingCount = 0),
              (this._resolveEmpty = i),
              (this._resolveIdle = i),
              !(
                "number" ==
                  typeof (e = Object.assign(
                    {
                      carryoverConcurrencyCount: !1,
                      intervalCap: 1 / 0,
                      interval: 0,
                      concurrency: 1 / 0,
                      autoStart: !0,
                      queueClass: s.default,
                    },
                    e,
                  )).intervalCap && e.intervalCap >= 1
              ))
            )
              throw new TypeError(
                `Expected \`intervalCap\` to be a number from 1 and up, got \`${null !== (n = null === (t = e.intervalCap) || void 0 === t ? void 0 : t.toString()) && void 0 !== n ? n : ""}\` (${typeof e.intervalCap})`,
              );
            if (
              void 0 === e.interval ||
              !(Number.isFinite(e.interval) && e.interval >= 0)
            )
              throw new TypeError(
                `Expected \`interval\` to be a finite number >= 0, got \`${null !== (o = null === (r = e.interval) || void 0 === r ? void 0 : r.toString()) && void 0 !== o ? o : ""}\` (${typeof e.interval})`,
              );
            ((this._carryoverConcurrencyCount = e.carryoverConcurrencyCount),
              (this._isIntervalIgnored =
                e.intervalCap === 1 / 0 || 0 === e.interval),
              (this._intervalCap = e.intervalCap),
              (this._interval = e.interval),
              (this._queue = new e.queueClass()),
              (this._queueClass = e.queueClass),
              (this.concurrency = e.concurrency),
              (this._timeout = e.timeout),
              (this._throwOnTimeout = !0 === e.throwOnTimeout),
              (this._isPaused = !1 === e.autoStart));
          }
          get _doesIntervalAllowAnother() {
            return (
              this._isIntervalIgnored || this._intervalCount < this._intervalCap
            );
          }
          get _doesConcurrentAllowAnother() {
            return this._pendingCount < this._concurrency;
          }
          _next() {
            (this._pendingCount--,
              this._tryToStartAnother(),
              this.emit("next"));
          }
          _resolvePromises() {
            (this._resolveEmpty(),
              (this._resolveEmpty = i),
              0 === this._pendingCount &&
                (this._resolveIdle(),
                (this._resolveIdle = i),
                this.emit("idle")));
          }
          _onResumeInterval() {
            (this._onInterval(),
              this._initializeIntervalIfNeeded(),
              (this._timeoutId = void 0));
          }
          _isIntervalPaused() {
            const e = Date.now();
            if (void 0 === this._intervalId) {
              const t = this._intervalEnd - e;
              if (!(t < 0))
                return (
                  void 0 === this._timeoutId &&
                    (this._timeoutId = setTimeout(() => {
                      this._onResumeInterval();
                    }, t)),
                  !0
                );
              this._intervalCount = this._carryoverConcurrencyCount
                ? this._pendingCount
                : 0;
            }
            return !1;
          }
          _tryToStartAnother() {
            if (0 === this._queue.size)
              return (
                this._intervalId && clearInterval(this._intervalId),
                (this._intervalId = void 0),
                this._resolvePromises(),
                !1
              );
            if (!this._isPaused) {
              const e = !this._isIntervalPaused();
              if (
                this._doesIntervalAllowAnother &&
                this._doesConcurrentAllowAnother
              ) {
                const t = this._queue.dequeue();
                return (
                  !!t &&
                  (this.emit("active"),
                  t(),
                  e && this._initializeIntervalIfNeeded(),
                  !0)
                );
              }
            }
            return !1;
          }
          _initializeIntervalIfNeeded() {
            this._isIntervalIgnored ||
              void 0 !== this._intervalId ||
              ((this._intervalId = setInterval(() => {
                this._onInterval();
              }, this._interval)),
              (this._intervalEnd = Date.now() + this._interval));
          }
          _onInterval() {
            (0 === this._intervalCount &&
              0 === this._pendingCount &&
              this._intervalId &&
              (clearInterval(this._intervalId), (this._intervalId = void 0)),
              (this._intervalCount = this._carryoverConcurrencyCount
                ? this._pendingCount
                : 0),
              this._processQueue());
          }
          _processQueue() {
            for (; this._tryToStartAnother(); );
          }
          get concurrency() {
            return this._concurrency;
          }
          set concurrency(e) {
            if (!("number" == typeof e && e >= 1))
              throw new TypeError(
                `Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`,
              );
            ((this._concurrency = e), this._processQueue());
          }
          async add(e, t = {}) {
            return new Promise((n, r) => {
              (this._queue.enqueue(async () => {
                (this._pendingCount++, this._intervalCount++);
                try {
                  const s =
                    void 0 === this._timeout && void 0 === t.timeout
                      ? e()
                      : o.default(
                          Promise.resolve(e()),
                          void 0 === t.timeout ? this._timeout : t.timeout,
                          () => {
                            (void 0 === t.throwOnTimeout
                              ? this._throwOnTimeout
                              : t.throwOnTimeout) && r(a);
                          },
                        );
                  n(await s);
                } catch (e) {
                  r(e);
                }
                this._next();
              }, t),
                this._tryToStartAnother(),
                this.emit("add"));
            });
          }
          async addAll(e, t) {
            return Promise.all(e.map(async (e) => this.add(e, t)));
          }
          start() {
            return this._isPaused
              ? ((this._isPaused = !1), this._processQueue(), this)
              : this;
          }
          pause() {
            this._isPaused = !0;
          }
          clear() {
            this._queue = new this._queueClass();
          }
          async onEmpty() {
            if (0 !== this._queue.size)
              return new Promise((e) => {
                const t = this._resolveEmpty;
                this._resolveEmpty = () => {
                  (t(), e());
                };
              });
          }
          async onIdle() {
            if (0 !== this._pendingCount || 0 !== this._queue.size)
              return new Promise((e) => {
                const t = this._resolveIdle;
                this._resolveIdle = () => {
                  (t(), e());
                };
              });
          }
          get size() {
            return this._queue.size;
          }
          sizeBy(e) {
            return this._queue.filter(e).length;
          }
          get pending() {
            return this._pendingCount;
          }
          get isPaused() {
            return this._isPaused;
          }
          get timeout() {
            return this._timeout;
          }
          set timeout(e) {
            this._timeout = e;
          }
        };
      },
      489: (e, t) => {
        "use strict";
        (Object.defineProperty(t, "__esModule", { value: !0 }),
          (t.default = function (e, t, n) {
            let r = 0,
              o = e.length;
            for (; o > 0; ) {
              const s = (o / 2) | 0;
              let i = r + s;
              n(e[i], t) <= 0 ? ((r = ++i), (o -= s + 1)) : (o = s);
            }
            return r;
          }));
      },
      506: (e, t, n) => {
        "use strict";
        Object.defineProperty(t, "__esModule", { value: !0 });
        const r = n(489);
        t.default = class {
          constructor() {
            this._queue = [];
          }
          enqueue(e, t) {
            const n = {
              priority: (t = Object.assign({ priority: 0 }, t)).priority,
              run: e,
            };
            if (this.size && this._queue[this.size - 1].priority >= t.priority)
              return void this._queue.push(n);
            const o = r.default(
              this._queue,
              n,
              (e, t) => t.priority - e.priority,
            );
            this._queue.splice(o, 0, n);
          }
          dequeue() {
            const e = this._queue.shift();
            return null == e ? void 0 : e.run;
          }
          filter(e) {
            return this._queue
              .filter((t) => t.priority === e.priority)
              .map((e) => e.run);
          }
          get size() {
            return this._queue.length;
          }
        };
      },
      512: (e, t, n) => {
        "use strict";
        const r = n(345);
        class o extends Error {
          constructor(e) {
            (super(e), (this.name = "TimeoutError"));
          }
        }
        const s = (e, t, n) =>
          new Promise((s, i) => {
            if ("number" != typeof t || t < 0)
              throw new TypeError(
                "Expected `milliseconds` to be a positive number",
              );
            if (t === 1 / 0) return void s(e);
            const a = setTimeout(() => {
              if ("function" == typeof n) {
                try {
                  s(n());
                } catch (e) {
                  i(e);
                }
                return;
              }
              const r =
                n instanceof Error
                  ? n
                  : new o(
                      "string" == typeof n
                        ? n
                        : `Promise timed out after ${t} milliseconds`,
                    );
              ("function" == typeof e.cancel && e.cancel(), i(r));
            }, t);
            r(e.then(s, i), () => {
              clearTimeout(a);
            });
          });
        ((e.exports = s),
          (e.exports.default = s),
          (e.exports.TimeoutError = o));
      },
      150: function (e, t) {
        var n, r, o;
        ("undefined" != typeof globalThis
          ? globalThis
          : "undefined" != typeof self && self,
          (r = [e]),
          (n = function (e) {
            "use strict";
            if (
              !(
                globalThis.chrome &&
                globalThis.chrome.runtime &&
                globalThis.chrome.runtime.id
              )
            )
              throw new Error(
                "This script should only be loaded in a browser extension.",
              );
            if (
              globalThis.browser &&
              globalThis.browser.runtime &&
              globalThis.browser.runtime.id
            )
              e.exports = globalThis.browser;
            else {
              const t =
                  "The message port closed before a response was received.",
                n = (e) => {
                  const n = {
                    alarms: {
                      clear: { minArgs: 0, maxArgs: 1 },
                      clearAll: { minArgs: 0, maxArgs: 0 },
                      get: { minArgs: 0, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                    },
                    bookmarks: {
                      create: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getChildren: { minArgs: 1, maxArgs: 1 },
                      getRecent: { minArgs: 1, maxArgs: 1 },
                      getSubTree: { minArgs: 1, maxArgs: 1 },
                      getTree: { minArgs: 0, maxArgs: 0 },
                      move: { minArgs: 2, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeTree: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    browserAction: {
                      disable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      enable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      getBadgeBackgroundColor: { minArgs: 1, maxArgs: 1 },
                      getBadgeText: { minArgs: 1, maxArgs: 1 },
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      openPopup: { minArgs: 0, maxArgs: 0 },
                      setBadgeBackgroundColor: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setBadgeText: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    browsingData: {
                      remove: { minArgs: 2, maxArgs: 2 },
                      removeCache: { minArgs: 1, maxArgs: 1 },
                      removeCookies: { minArgs: 1, maxArgs: 1 },
                      removeDownloads: { minArgs: 1, maxArgs: 1 },
                      removeFormData: { minArgs: 1, maxArgs: 1 },
                      removeHistory: { minArgs: 1, maxArgs: 1 },
                      removeLocalStorage: { minArgs: 1, maxArgs: 1 },
                      removePasswords: { minArgs: 1, maxArgs: 1 },
                      removePluginData: { minArgs: 1, maxArgs: 1 },
                      settings: { minArgs: 0, maxArgs: 0 },
                    },
                    commands: { getAll: { minArgs: 0, maxArgs: 0 } },
                    contextMenus: {
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeAll: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    cookies: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 1, maxArgs: 1 },
                      getAllCookieStores: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    },
                    devtools: {
                      inspectedWindow: {
                        eval: { minArgs: 1, maxArgs: 2, singleCallbackArg: !1 },
                      },
                      panels: {
                        create: {
                          minArgs: 3,
                          maxArgs: 3,
                          singleCallbackArg: !0,
                        },
                        elements: {
                          createSidebarPane: { minArgs: 1, maxArgs: 1 },
                        },
                      },
                    },
                    downloads: {
                      cancel: { minArgs: 1, maxArgs: 1 },
                      download: { minArgs: 1, maxArgs: 1 },
                      erase: { minArgs: 1, maxArgs: 1 },
                      getFileIcon: { minArgs: 1, maxArgs: 2 },
                      open: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      pause: { minArgs: 1, maxArgs: 1 },
                      removeFile: { minArgs: 1, maxArgs: 1 },
                      resume: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    extension: {
                      isAllowedFileSchemeAccess: { minArgs: 0, maxArgs: 0 },
                      isAllowedIncognitoAccess: { minArgs: 0, maxArgs: 0 },
                    },
                    history: {
                      addUrl: { minArgs: 1, maxArgs: 1 },
                      deleteAll: { minArgs: 0, maxArgs: 0 },
                      deleteRange: { minArgs: 1, maxArgs: 1 },
                      deleteUrl: { minArgs: 1, maxArgs: 1 },
                      getVisits: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                    },
                    i18n: {
                      detectLanguage: { minArgs: 1, maxArgs: 1 },
                      getAcceptLanguages: { minArgs: 0, maxArgs: 0 },
                    },
                    identity: { launchWebAuthFlow: { minArgs: 1, maxArgs: 1 } },
                    idle: { queryState: { minArgs: 1, maxArgs: 1 } },
                    management: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getSelf: { minArgs: 0, maxArgs: 0 },
                      setEnabled: { minArgs: 2, maxArgs: 2 },
                      uninstallSelf: { minArgs: 0, maxArgs: 1 },
                    },
                    notifications: {
                      clear: { minArgs: 1, maxArgs: 1 },
                      create: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getPermissionLevel: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    pageAction: {
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      hide: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    permissions: {
                      contains: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      request: { minArgs: 1, maxArgs: 1 },
                    },
                    runtime: {
                      getBackgroundPage: { minArgs: 0, maxArgs: 0 },
                      getPlatformInfo: { minArgs: 0, maxArgs: 0 },
                      openOptionsPage: { minArgs: 0, maxArgs: 0 },
                      requestUpdateCheck: { minArgs: 0, maxArgs: 0 },
                      sendMessage: { minArgs: 1, maxArgs: 3 },
                      sendNativeMessage: { minArgs: 2, maxArgs: 2 },
                      setUninstallURL: { minArgs: 1, maxArgs: 1 },
                    },
                    sessions: {
                      getDevices: { minArgs: 0, maxArgs: 1 },
                      getRecentlyClosed: { minArgs: 0, maxArgs: 1 },
                      restore: { minArgs: 0, maxArgs: 1 },
                    },
                    storage: {
                      local: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                      managed: {
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                      },
                      sync: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                    },
                    tabs: {
                      captureVisibleTab: { minArgs: 0, maxArgs: 2 },
                      create: { minArgs: 1, maxArgs: 1 },
                      detectLanguage: { minArgs: 0, maxArgs: 1 },
                      discard: { minArgs: 0, maxArgs: 1 },
                      duplicate: { minArgs: 1, maxArgs: 1 },
                      executeScript: { minArgs: 1, maxArgs: 2 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 0 },
                      getZoom: { minArgs: 0, maxArgs: 1 },
                      getZoomSettings: { minArgs: 0, maxArgs: 1 },
                      goBack: { minArgs: 0, maxArgs: 1 },
                      goForward: { minArgs: 0, maxArgs: 1 },
                      highlight: { minArgs: 1, maxArgs: 1 },
                      insertCSS: { minArgs: 1, maxArgs: 2 },
                      move: { minArgs: 2, maxArgs: 2 },
                      query: { minArgs: 1, maxArgs: 1 },
                      reload: { minArgs: 0, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeCSS: { minArgs: 1, maxArgs: 2 },
                      sendMessage: { minArgs: 2, maxArgs: 3 },
                      setZoom: { minArgs: 1, maxArgs: 2 },
                      setZoomSettings: { minArgs: 1, maxArgs: 2 },
                      update: { minArgs: 1, maxArgs: 2 },
                    },
                    topSites: { get: { minArgs: 0, maxArgs: 0 } },
                    webNavigation: {
                      getAllFrames: { minArgs: 1, maxArgs: 1 },
                      getFrame: { minArgs: 1, maxArgs: 1 },
                    },
                    webRequest: {
                      handlerBehaviorChanged: { minArgs: 0, maxArgs: 0 },
                    },
                    windows: {
                      create: { minArgs: 0, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 1 },
                      getLastFocused: { minArgs: 0, maxArgs: 1 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                  };
                  if (0 === Object.keys(n).length)
                    throw new Error(
                      "api-metadata.json has not been included in browser-polyfill",
                    );
                  class r extends WeakMap {
                    constructor(e, t = void 0) {
                      (super(t), (this.createItem = e));
                    }
                    get(e) {
                      return (
                        this.has(e) || this.set(e, this.createItem(e)),
                        super.get(e)
                      );
                    }
                  }
                  const o = (e) =>
                      e && "object" == typeof e && "function" == typeof e.then,
                    s =
                      (t, n) =>
                      (...r) => {
                        e.runtime.lastError
                          ? t.reject(new Error(e.runtime.lastError.message))
                          : n.singleCallbackArg ||
                              (r.length <= 1 && !1 !== n.singleCallbackArg)
                            ? t.resolve(r[0])
                            : t.resolve(r);
                      },
                    i = (e) => (1 == e ? "argument" : "arguments"),
                    a = (e, t) =>
                      function (n, ...r) {
                        if (r.length < t.minArgs)
                          throw new Error(
                            `Expected at least ${t.minArgs} ${i(t.minArgs)} for ${e}(), got ${r.length}`,
                          );
                        if (r.length > t.maxArgs)
                          throw new Error(
                            `Expected at most ${t.maxArgs} ${i(t.maxArgs)} for ${e}(), got ${r.length}`,
                          );
                        return new Promise((o, i) => {
                          if (t.fallbackToNoCallback)
                            try {
                              n[e](...r, s({ resolve: o, reject: i }, t));
                            } catch (s) {
                              (console.warn(
                                `${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,
                                s,
                              ),
                                n[e](...r),
                                (t.fallbackToNoCallback = !1),
                                (t.noCallback = !0),
                                o());
                            }
                          else
                            t.noCallback
                              ? (n[e](...r), o())
                              : n[e](...r, s({ resolve: o, reject: i }, t));
                        });
                      },
                    d = (e, t, n) =>
                      new Proxy(t, { apply: (t, r, o) => n.call(r, e, ...o) });
                  let l = Function.call.bind(Object.prototype.hasOwnProperty);
                  const c = (e, t = {}, n = {}) => {
                      let r = Object.create(null),
                        o = {
                          has: (t, n) => n in e || n in r,
                          get(o, s, i) {
                            if (s in r) return r[s];
                            if (!(s in e)) return;
                            let u = e[s];
                            if ("function" == typeof u)
                              if ("function" == typeof t[s])
                                u = d(e, e[s], t[s]);
                              else if (l(n, s)) {
                                let t = a(s, n[s]);
                                u = d(e, e[s], t);
                              } else u = u.bind(e);
                            else if (
                              "object" == typeof u &&
                              null !== u &&
                              (l(t, s) || l(n, s))
                            )
                              u = c(u, t[s], n[s]);
                            else {
                              if (!l(n, "*"))
                                return (
                                  Object.defineProperty(r, s, {
                                    configurable: !0,
                                    enumerable: !0,
                                    get: () => e[s],
                                    set(t) {
                                      e[s] = t;
                                    },
                                  }),
                                  u
                                );
                              u = c(u, t[s], n["*"]);
                            }
                            return ((r[s] = u), u);
                          },
                          set: (t, n, o, s) => (
                            n in r ? (r[n] = o) : (e[n] = o),
                            !0
                          ),
                          defineProperty: (e, t, n) =>
                            Reflect.defineProperty(r, t, n),
                          deleteProperty: (e, t) =>
                            Reflect.deleteProperty(r, t),
                        },
                        s = Object.create(e);
                      return new Proxy(s, o);
                    },
                    u = (e) => ({
                      addListener(t, n, ...r) {
                        t.addListener(e.get(n), ...r);
                      },
                      hasListener: (t, n) => t.hasListener(e.get(n)),
                      removeListener(t, n) {
                        t.removeListener(e.get(n));
                      },
                    }),
                    g = new r((e) =>
                      "function" != typeof e
                        ? e
                        : function (t) {
                            const n = c(
                              t,
                              {},
                              { getContent: { minArgs: 0, maxArgs: 0 } },
                            );
                            e(n);
                          },
                    ),
                    m = new r((e) =>
                      "function" != typeof e
                        ? e
                        : function (t, n, r) {
                            let s,
                              i,
                              a = !1,
                              d = new Promise((e) => {
                                s = function (t) {
                                  ((a = !0), e(t));
                                };
                              });
                            try {
                              i = e(t, n, s);
                            } catch (e) {
                              i = Promise.reject(e);
                            }
                            const l = !0 !== i && o(i);
                            if (!0 !== i && !l && !a) return !1;
                            const c = (e) => {
                              e.then(
                                (e) => {
                                  r(e);
                                },
                                (e) => {
                                  let t;
                                  ((t =
                                    e &&
                                    (e instanceof Error ||
                                      "string" == typeof e.message)
                                      ? e.message
                                      : "An unexpected error occurred"),
                                    r({
                                      __mozWebExtensionPolyfillReject__: !0,
                                      message: t,
                                    }));
                                },
                              ).catch((e) => {
                                console.error(
                                  "Failed to send onMessage rejected reply",
                                  e,
                                );
                              });
                            };
                            return (c(l ? i : d), !0);
                          },
                    ),
                    f = ({ reject: n, resolve: r }, o) => {
                      e.runtime.lastError
                        ? e.runtime.lastError.message === t
                          ? r()
                          : n(new Error(e.runtime.lastError.message))
                        : o && o.__mozWebExtensionPolyfillReject__
                          ? n(new Error(o.message))
                          : r(o);
                    },
                    p = (e, t, n, ...r) => {
                      if (r.length < t.minArgs)
                        throw new Error(
                          `Expected at least ${t.minArgs} ${i(t.minArgs)} for ${e}(), got ${r.length}`,
                        );
                      if (r.length > t.maxArgs)
                        throw new Error(
                          `Expected at most ${t.maxArgs} ${i(t.maxArgs)} for ${e}(), got ${r.length}`,
                        );
                      return new Promise((e, t) => {
                        const o = f.bind(null, { resolve: e, reject: t });
                        (r.push(o), n.sendMessage(...r));
                      });
                    },
                    h = {
                      devtools: { network: { onRequestFinished: u(g) } },
                      runtime: {
                        onMessage: u(m),
                        onMessageExternal: u(m),
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 1,
                          maxArgs: 3,
                        }),
                      },
                      tabs: {
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 2,
                          maxArgs: 3,
                        }),
                      },
                    },
                    w = {
                      clear: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    };
                  return (
                    (n.privacy = {
                      network: { "*": w },
                      services: { "*": w },
                      websites: { "*": w },
                    }),
                    c(e, h, n)
                  );
                };
              e.exports = n(chrome);
            }
          }),
          void 0 === (o = "function" == typeof n ? n.apply(t, r) : n) ||
            (e.exports = o));
      },
    },
    t = {};
  function n(r) {
    var o = t[r];
    if (void 0 !== o) return o.exports;
    var s = (t[r] = { exports: {} });
    return (e[r].call(s.exports, s, s.exports, n), s.exports);
  }
  ((n.m = e),
    (n.n = (e) => {
      var t = e && e.__esModule ? () => e.default : () => e;
      return (n.d(t, { a: t }), t);
    }),
    (n.d = (e, t) => {
      for (var r in t)
        n.o(t, r) &&
          !n.o(e, r) &&
          Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
    }),
    (n.u = (e) => "js/" + e + ".js"),
    (n.g = (function () {
      if ("object" == typeof globalThis) return globalThis;
      try {
        return this || new Function("return this")();
      } catch (e) {
        if ("object" == typeof window) return window;
      }
    })()),
    (n.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t)),
    (() => {
      var e;
      n.g.importScripts && (e = n.g.location + "");
      var t = n.g.document;
      if (!e && t && (t.currentScript && (e = t.currentScript.src), !e)) {
        var r = t.getElementsByTagName("script");
        r.length && (e = r[r.length - 1].src);
      }
      if (!e)
        throw new Error(
          "Automatic publicPath is not supported in this browser",
        );
      ((e = e
        .replace(/#.*$/, "")
        .replace(/\?.*$/, "")
        .replace(/\/[^\/]+$/, "/")),
        (n.p = e + "../"));
    })(),
    (n.b = document.baseURI || self.location.href),
    (() => {
      "use strict";
      function e(e, t, n, r) {
        return new (n || (n = Promise))(function (o, s) {
          function i(e) {
            try {
              d(r.next(e));
            } catch (e) {
              s(e);
            }
          }
          function a(e) {
            try {
              d(r.throw(e));
            } catch (e) {
              s(e);
            }
          }
          function d(e) {
            var t;
            e.done
              ? o(e.value)
              : ((t = e.value),
                t instanceof n
                  ? t
                  : new n(function (e) {
                      e(t);
                    })).then(i, a);
          }
          d((r = r.apply(e, t || [])).next());
        });
      }
      Object.create;
      function t(e) {
        var t = "function" == typeof Symbol && Symbol.iterator,
          n = t && e[t],
          r = 0;
        if (n) return n.call(e);
        if (e && "number" == typeof e.length)
          return {
            next: function () {
              return (
                e && r >= e.length && (e = void 0),
                { value: e && e[r++], done: !e }
              );
            },
          };
        throw new TypeError(
          t ? "Object is not iterable." : "Symbol.iterator is not defined.",
        );
      }
      function r(e) {
        return this instanceof r ? ((this.v = e), this) : new r(e);
      }
      function o(e, t, n) {
        if (!Symbol.asyncIterator)
          throw new TypeError("Symbol.asyncIterator is not defined.");
        var o,
          s = n.apply(e, t || []),
          i = [];
        return (
          (o = {}),
          a("next"),
          a("throw"),
          a("return"),
          (o[Symbol.asyncIterator] = function () {
            return this;
          }),
          o
        );
        function a(e) {
          s[e] &&
            (o[e] = function (t) {
              return new Promise(function (n, r) {
                i.push([e, t, n, r]) > 1 || d(e, t);
              });
            });
        }
        function d(e, t) {
          try {
            (n = s[e](t)).value instanceof r
              ? Promise.resolve(n.value.v).then(l, c)
              : u(i[0][2], n);
          } catch (e) {
            u(i[0][3], e);
          }
          var n;
        }
        function l(e) {
          d("next", e);
        }
        function c(e) {
          d("throw", e);
        }
        function u(e, t) {
          (e(t), i.shift(), i.length && d(i[0][0], i[0][1]));
        }
      }
      function s(e) {
        if (!Symbol.asyncIterator)
          throw new TypeError("Symbol.asyncIterator is not defined.");
        var n,
          r = e[Symbol.asyncIterator];
        return r
          ? r.call(e)
          : ((e = t(e)),
            (n = {}),
            o("next"),
            o("throw"),
            o("return"),
            (n[Symbol.asyncIterator] = function () {
              return this;
            }),
            n);
        function o(t) {
          n[t] =
            e[t] &&
            function (n) {
              return new Promise(function (r, o) {
                (function (e, t, n, r) {
                  Promise.resolve(r).then(function (t) {
                    e({ value: t, done: n });
                  }, t);
                })(r, o, (n = e[t](n)).done, n.value);
              });
            };
        }
      }
      Object.create;
      var i,
        a,
        d,
        l,
        c,
        u,
        g,
        m,
        f,
        p,
        h,
        w,
        v,
        y,
        A = n(150),
        x = n.n(A);
      (!(function (e) {
        ((e.download = "download"),
          (e.progress = "progress"),
          (e.end = "end"),
          (e.cancle = "cancle"),
          (e.merge = "merge"),
          (e.error = "error"));
      })(i || (i = {})),
        (function (e) {
          ((e.downloadingToDownloaded = "downloadingToDownloaded"),
            (e.downloadToDownloaded = "downloadToDownloaded"));
        })(a || (a = {})),
        (function (e) {
          ((e[(e.pre = 0)] = "pre"),
            (e[(e.after = 1)] = "after"),
            (e[(e.getExtDrmKey = 2)] = "getExtDrmKey"));
        })(d || (d = {})),
        (function (e) {
          ((e[(e.single = 0)] = "single"),
            (e[(e.bulk = 1)] = "bulk"),
            (e[(e.bloburl = 2)] = "bloburl"),
            (e[(e.changeUrl = 3)] = "changeUrl"),
            (e[(e.login = 4)] = "login"),
            (e[(e.googleLogin = 5)] = "googleLogin"),
            (e[(e.register = 6)] = "register"),
            (e[(e.sendEmailCode = 7)] = "sendEmailCode"),
            (e[(e.getDrmSecretKey = 8)] = "getDrmSecretKey"),
            (e[(e.getConfig = 9)] = "getConfig"),
            (e[(e.getMemberInfo = 10)] = "getMemberInfo"),
            (e[(e.updateNoPerDayDownloadCount = 11)] =
              "updateNoPerDayDownloadCount"));
        })(l || (l = {})),
        (function (e) {
          ((e[(e.goSubscribe = 0)] = "goSubscribe"),
            (e[(e.pureNotice = 1)] = "pureNotice"),
            (e[(e.drmLicense = 2)] = "drmLicense"),
            (e[(e.retryMessage = 3)] = "retryMessage"),
            (e[(e.serverError = 4)] = "serverError"));
        })(c || (c = {})),
        (function (e) {
          ((e[(e.Edge = 0)] = "Edge"),
            (e[(e.Chrome = 1)] = "Chrome"),
            (e[(e.Firefox = 2)] = "Firefox"),
            (e[(e.Opera = 3)] = "Opera"),
            (e[(e.Safari = 4)] = "Safari"),
            (e[(e.Unknown = 5)] = "Unknown"));
        })(u || (u = {})),
        (function (e) {
          ((e.default = "log"), (e.warn = "warn"), (e.error = "error"));
        })(g || (g = {})),
        (function (e) {
          ((e.install = "install"),
            (e.uninstall = "uninstall"),
            (e.downloadSignalUnkown = "downloadSignalUnkown"),
            (e.downloadSignalImg = "downloadSignalImg"),
            (e.downloadSignalVideo = "downloadSignalVideo"),
            (e.downloadBulk = "downloadBulk"),
            (e.changeUrl = "changeUrl"),
            (e.register = "register"),
            (e.login = "login"),
            (e.googleLogin = "googleLogin"),
            (e.sendEmailCode = "sendEmailCode"),
            (e.uploadFiles = "uploadFiles"),
            (e.concatVideoAndAudio = "concatVideoAndAudio"));
        })(m || (m = {})),
        (function (e) {
          ((e.downloadSuccess = "downloadSuccess"),
            (e.downloadError = "downloadError"),
            (e.downloadCancle = "downloadCancle"),
            (e.downloadWating = "downloadWating"),
            (e.downloadPrepare = "downloadPrepare"),
            (e.downloadStuck = "downloadStuck"));
        })(f || (f = {})),
        (function (e) {
          ((e.addOrUpdateDownloadingInfo = "addOrUpdateDownloadingInfo"),
            (e.updateDownloadStatus = "updateDownloadStatus"));
        })(p || (p = {})),
        (function (e) {
          e[(e.refresh = 0)] = "refresh";
        })(h || (h = {})),
        (function (e) {
          ((e.downloading = "downloading"),
            (e.downloaded = "downloaded"),
            (e.download = "download"),
            (e.all = "all"),
            (e.quota = "quota"));
        })(w || (w = {})),
        (function (e) {
          ((e.processVideo = "processVideo"),
            (e.processVideoInWeb = "processVideoInWeb"),
            (e.processVideoByUrl = "processVideoByUrl"));
        })(v || (v = {})),
        (function (e) {
          ((e.serverError = "serverError"), (e.tip = "tip"));
        })(y || (y = {})));
      var b;
      !(function (e) {
        ((e.LOAD = "LOAD"),
          (e.EXEC = "EXEC"),
          (e.WRITE_FILE = "WRITE_FILE"),
          (e.READ_FILE = "READ_FILE"),
          (e.DELETE_FILE = "DELETE_FILE"),
          (e.RENAME = "RENAME"),
          (e.CREATE_DIR = "CREATE_DIR"),
          (e.LIST_DIR = "LIST_DIR"),
          (e.DELETE_DIR = "DELETE_DIR"),
          (e.ERROR = "ERROR"),
          (e.DOWNLOAD = "DOWNLOAD"),
          (e.PROGRESS = "PROGRESS"),
          (e.LOG = "LOG"),
          (e.MOUNT = "MOUNT"),
          (e.UNMOUNT = "UNMOUNT"));
      })(b || (b = {}));
      const O = (() => {
          let e = 0;
          return () => e++;
        })(),
        E =
          (new Error("unknown message type"),
          new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first")),
        I = new Error("called FFmpeg.terminate()");
      new Error("failed to import ffmpeg-core.js");
      class S {
        #e = null;
        #t = {};
        #n = {};
        #r = [];
        #o = [];
        loaded = !1;
        #s = () => {
          this.#e &&
            (this.#e.onmessage = ({ data: { id: e, type: t, data: n } }) => {
              switch (t) {
                case b.LOAD:
                  ((this.loaded = !0), this.#t[e](n));
                  break;
                case b.MOUNT:
                case b.UNMOUNT:
                case b.EXEC:
                case b.WRITE_FILE:
                case b.READ_FILE:
                case b.DELETE_FILE:
                case b.RENAME:
                case b.CREATE_DIR:
                case b.LIST_DIR:
                case b.DELETE_DIR:
                  this.#t[e](n);
                  break;
                case b.LOG:
                  this.#r.forEach((e) => e(n));
                  break;
                case b.PROGRESS:
                  this.#o.forEach((e) => e(n));
                  break;
                case b.ERROR:
                  this.#n[e](n);
              }
              (delete this.#t[e], delete this.#n[e]);
            });
        };
        #i = ({ type: e, data: t }, n = [], r) =>
          this.#e
            ? new Promise((o, s) => {
                const i = O();
                (this.#e && this.#e.postMessage({ id: i, type: e, data: t }, n),
                  (this.#t[i] = o),
                  (this.#n[i] = s),
                  r?.addEventListener(
                    "abort",
                    () => {
                      s(
                        new DOMException(
                          `Message # ${i} was aborted`,
                          "AbortError",
                        ),
                      );
                    },
                    { once: !0 },
                  ));
              })
            : Promise.reject(E);
        on(e, t) {
          "log" === e ? this.#r.push(t) : "progress" === e && this.#o.push(t);
        }
        off(e, t) {
          "log" === e
            ? (this.#r = this.#r.filter((e) => e !== t))
            : "progress" === e && (this.#o = this.#o.filter((e) => e !== t));
        }
        load = ({ classWorkerURL: e, ...t } = {}, { signal: r } = {}) => (
          this.#e ||
            ((this.#e = e
              ? new Worker(
                  new URL(
                    e,
                    "file:///D:/vsCodeProjects/UdemyDownloader/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js",
                  ),
                  { type: "module" },
                )
              : new Worker(new URL(n.p + n.u(439), n.b), { type: void 0 })),
            this.#s()),
          this.#i({ type: b.LOAD, data: t }, void 0, r)
        );
        exec = (e, t = -1, { signal: n } = {}) =>
          this.#i({ type: b.EXEC, data: { args: e, timeout: t } }, void 0, n);
        terminate = () => {
          const e = Object.keys(this.#n);
          for (const t of e)
            (this.#n[t](I), delete this.#n[t], delete this.#t[t]);
          this.#e &&
            (this.#e.terminate(), (this.#e = null), (this.loaded = !1));
        };
        writeFile = (e, t, { signal: n } = {}) => {
          const r = [];
          return (
            t instanceof Uint8Array && r.push(t.buffer),
            this.#i({ type: b.WRITE_FILE, data: { path: e, data: t } }, r, n)
          );
        };
        mount = (e, t, n) =>
          this.#i(
            { type: b.MOUNT, data: { fsType: e, options: t, mountPoint: n } },
            [],
          );
        unmount = (e) =>
          this.#i({ type: b.UNMOUNT, data: { mountPoint: e } }, []);
        readFile = (e, t = "binary", { signal: n } = {}) =>
          this.#i(
            { type: b.READ_FILE, data: { path: e, encoding: t } },
            void 0,
            n,
          );
        deleteFile = (e, { signal: t } = {}) =>
          this.#i({ type: b.DELETE_FILE, data: { path: e } }, void 0, t);
        rename = (e, t, { signal: n } = {}) =>
          this.#i(
            { type: b.RENAME, data: { oldPath: e, newPath: t } },
            void 0,
            n,
          );
        createDir = (e, { signal: t } = {}) =>
          this.#i({ type: b.CREATE_DIR, data: { path: e } }, void 0, t);
        listDir = (e, { signal: t } = {}) =>
          this.#i({ type: b.LIST_DIR, data: { path: e } }, void 0, t);
        deleteDir = (e, { signal: t } = {}) =>
          this.#i({ type: b.DELETE_DIR, data: { path: e } }, void 0, t);
      }
      var k = n(860);
      var _;
      !(function (t) {
        let n;
        const r = () => {
          var e;
          null === (e = document.getElementById("addOnInfoWrapperid")) ||
            void 0 === e ||
            e.remove();
          const t = document.createElement("div");
          return (
            (t.id = "addOnInfoWrapperid"),
            (t.innerHTML =
              '\n    <div class="modal" id="modal">\n        <div class="modal-header">\n            <div id="addon-info-title"></div>\n        </div>\n        <div class="modal-content">\n        </div>\n        <div class="modal-footer">            \n        </div>\n    </div>\n    '),
            document.body.appendChild(t),
            t
          );
        };
        function o(e, t, n) {
          const r = document.createElement("button");
          return (
            r.classList.add("btn", e),
            (r.textContent = t),
            r.addEventListener("click", function () {
              (null != n && n(), i());
            }),
            r
          );
        }
        function s() {
          const e = document.getElementById("modal");
          ((e.style.display = "block"),
            setTimeout(() => {
              e.classList.add("show");
            }, 10));
        }
        function i() {
          const e = document.getElementById("modal");
          (e.classList.remove("show"),
            setTimeout(() => {
              ((e.style.display = "none"), clearInterval(n));
            }, 300));
        }
        ((t.displayMessage = function (e, n = 10) {
          B(location.href) &&
            (e.type == c.goSubscribe
              ? (console.log(e),
                console.log(e.mainAction),
                e.mainAction && "blank" == e.mainAction
                  ? (console.log(111),
                    t.openModalWithButton(
                      e.title,
                      e.text,
                      e.mainText,
                      () => {
                        window.open(e.mainUrl, "_blank");
                      },
                      e.subText,
                      null,
                    ))
                  : t.openModalWithButton(
                      e.title,
                      e.text,
                      e.mainText,
                      () => {
                        window.location.href = e.mainUrl;
                      },
                      e.subText,
                      null,
                    ))
              : e.type == c.pureNotice &&
                t.openModalWithTimer(e.title, e.text, n));
        }),
          (t.openModalWithTimer = function (t, o, a = 10) {
            if (B(location.href)) {
              r();
              const d = document.getElementById("addon-info-title"),
                l = document.querySelector(".modal-content"),
                c = document.querySelector(".modal-footer");
              (clearInterval(n),
                (l.innerHTML = o),
                (c.innerHTML = ""),
                (d.innerHTML = t));
              const u = document.createElement("div");
              ((u.id = "countdown"),
                (u.textContent = `close in ${a}s`),
                c.appendChild(u));
              let g = a;
              n = setInterval(() => {
                g <= 0
                  ? (clearInterval(n), i())
                  : ((u.textContent = `close in ${g}s`), g--);
              }, 1e3);
              const m = document.querySelector(".noticButtonP .noticA");
              (m &&
                (m.onclick = () =>
                  e(this, void 0, void 0, function* () {
                    let e = yield R("discordUrl");
                    (window.open(e + "", "_blank"),
                      yield F("isJumpDiscord", !0));
                  })),
                s());
            }
          }),
          (t.openModalWithButton = function (e, t, i, a, d, l) {
            if (B(location.href)) {
              r();
              const c = document.getElementById("addon-info-title"),
                u = document.querySelector(".modal-content"),
                g = document.querySelector(".modal-footer");
              if (
                (clearInterval(n),
                (u.innerHTML = t),
                (g.innerHTML = ""),
                (c.innerHTML = e),
                null != i)
              ) {
                const e = o("btn-wishlist", i, a);
                g.appendChild(e);
              }
              if (null != d) {
                const e = o("btn-no-thanks", d, l);
                g.appendChild(e);
              }
              const m = document.querySelector(".notice_openSiderpanle");
              (m &&
                (m.onclick = () => {
                  let e = x().runtime.connect({ name: "openSidepanels" });
                  (e.postMessage({}), e.disconnect());
                }),
                s());
            }
          }));
      })(_ || (_ = {}));
      var M = (function () {
        var e = "undefined" != typeof self ? self : this,
          t = {
            navigator: void 0 !== e.navigator ? e.navigator : {},
            infoMap: {
              engine: ["WebKit", "Trident", "Gecko", "Presto"],
              browser: [
                "Safari",
                "Chrome",
                "Edge",
                "IE",
                "Firefox",
                "Firefox Focus",
                "Chromium",
                "Opera",
                "Vivaldi",
                "Yandex",
                "Arora",
                "Lunascape",
                "QupZilla",
                "Coc Coc",
                "Kindle",
                "Iceweasel",
                "Konqueror",
                "Iceape",
                "SeaMonkey",
                "Epiphany",
                "360",
                "360SE",
                "360EE",
                "UC",
                "QQBrowser",
                "QQ",
                "Baidu",
                "Maxthon",
                "Sogou",
                "LBBROWSER",
                "2345Explorer",
                "TheWorld",
                "XiaoMi",
                "Quark",
                "Qiyu",
                "Wechat",
                ,
                "WechatWork",
                "Taobao",
                "Alipay",
                "Weibo",
                "Douban",
                "Suning",
                "iQiYi",
              ],
              os: [
                "Windows",
                "Linux",
                "Mac OS",
                "Android",
                "Ubuntu",
                "FreeBSD",
                "Debian",
                "iOS",
                "Windows Phone",
                "BlackBerry",
                "MeeGo",
                "Symbian",
                "Chrome OS",
                "WebOS",
              ],
              device: ["Mobile", "Tablet", "iPad"],
            },
          },
          n = {
            createUUID: function () {
              for (var e = [], t = "0123456789abcdef", n = 0; n < 36; n++)
                e[n] = t.substr(Math.floor(16 * Math.random()), 1);
              return (
                (e[14] = "4"),
                (e[19] = t.substr((3 & e[19]) | 8, 1)),
                (e[8] = e[13] = e[18] = e[23] = "-"),
                e.join("")
              );
            },
            getDate: function () {
              var e = new Date(),
                t = e.getFullYear(),
                n = e.getMonth() + 1,
                r = e.getDate(),
                o = e.getHours(),
                s = e.getMinutes(),
                i = e.getSeconds();
              return ""
                .concat(t.toString(), "/")
                .concat(n.toString(), "/")
                .concat(r.toString(), " ")
                .concat(o.toString(), ":")
                .concat(s.toString(), ":")
                .concat(i.toString());
            },
            getTimezoneOffset: function () {
              return new Date().getTimezoneOffset();
            },
            getTimezone: function () {
              return Intl.DateTimeFormat().resolvedOptions().timeZone;
            },
            getMatchMap: function (e) {
              return {
                Trident: e.indexOf("Trident") > -1 || e.indexOf("NET CLR") > -1,
                Presto: e.indexOf("Presto") > -1,
                WebKit: e.indexOf("AppleWebKit") > -1,
                Gecko: e.indexOf("Gecko/") > -1,
                Safari: e.indexOf("Safari") > -1,
                Chrome: e.indexOf("Chrome") > -1 || e.indexOf("CriOS") > -1,
                IE: e.indexOf("MSIE") > -1 || e.indexOf("Trident") > -1,
                Edge: e.indexOf("Edge") > -1,
                Firefox: e.indexOf("Firefox") > -1 || e.indexOf("FxiOS") > -1,
                "Firefox Focus": e.indexOf("Focus") > -1,
                Chromium: e.indexOf("Chromium") > -1,
                Opera: e.indexOf("Opera") > -1 || e.indexOf("OPR") > -1,
                Vivaldi: e.indexOf("Vivaldi") > -1,
                Yandex: e.indexOf("YaBrowser") > -1,
                Arora: e.indexOf("Arora") > -1,
                Lunascape: e.indexOf("Lunascape") > -1,
                QupZilla: e.indexOf("QupZilla") > -1,
                "Coc Coc": e.indexOf("coc_coc_browser") > -1,
                Kindle: e.indexOf("Kindle") > -1 || e.indexOf("Silk/") > -1,
                Iceweasel: e.indexOf("Iceweasel") > -1,
                Konqueror: e.indexOf("Konqueror") > -1,
                Iceape: e.indexOf("Iceape") > -1,
                SeaMonkey: e.indexOf("SeaMonkey") > -1,
                Epiphany: e.indexOf("Epiphany") > -1,
                360:
                  e.indexOf("QihooBrowser") > -1 || e.indexOf("QHBrowser") > -1,
                "360EE": e.indexOf("360EE") > -1,
                "360SE": e.indexOf("360SE") > -1,
                UC: e.indexOf("UC") > -1 || e.indexOf(" UBrowser") > -1,
                QQBrowser: e.indexOf("QQBrowser") > -1,
                QQ: e.indexOf("QQ/") > -1,
                Baidu: e.indexOf("Baidu") > -1 || e.indexOf("BIDUBrowser") > -1,
                Maxthon: e.indexOf("Maxthon") > -1,
                Sogou: e.indexOf("MetaSr") > -1 || e.indexOf("Sogou") > -1,
                LBBROWSER:
                  e.indexOf("LBBROWSER") > -1 || e.indexOf("LieBaoFast") > -1,
                "2345Explorer": e.indexOf("2345Explorer") > -1,
                TheWorld: e.indexOf("TheWorld") > -1,
                XiaoMi: e.indexOf("MiuiBrowser") > -1,
                Quark: e.indexOf("Quark") > -1,
                Qiyu: e.indexOf("Qiyu") > -1,
                Wechat: e.indexOf("MicroMessenger") > -1,
                WechatWork: e.indexOf("wxwork/") > -1,
                Taobao: e.indexOf("AliApp(TB") > -1,
                Alipay: e.indexOf("AliApp(AP") > -1,
                Weibo: e.indexOf("Weibo") > -1,
                Douban: e.indexOf("com.douban.frodo") > -1,
                Suning: e.indexOf("SNEBUY-APP") > -1,
                iQiYi: e.indexOf("IqiyiApp") > -1,
                DingTalk: e.indexOf("DingTalk") > -1,
                Vivo: e.indexOf("VivoBrowser") > -1,
                Huawei:
                  e.indexOf("HuaweiBrowser") > -1 ||
                  e.indexOf("HUAWEI/") > -1 ||
                  e.indexOf("HONOR") > -1 ||
                  e.indexOf("HBPC/") > -1,
                Windows: e.indexOf("Windows") > -1,
                Linux: e.indexOf("Linux") > -1 || e.indexOf("X11") > -1,
                "Mac OS": e.indexOf("Macintosh") > -1,
                Android: e.indexOf("Android") > -1 || e.indexOf("Adr") > -1,
                Ubuntu: e.indexOf("Ubuntu") > -1,
                FreeBSD: e.indexOf("FreeBSD") > -1,
                Debian: e.indexOf("Debian") > -1,
                "Windows Phone":
                  e.indexOf("IEMobile") > -1 || e.indexOf("Windows Phone") > -1,
                BlackBerry:
                  e.indexOf("BlackBerry") > -1 || e.indexOf("RIM") > -1,
                MeeGo: e.indexOf("MeeGo") > -1,
                Symbian: e.indexOf("Symbian") > -1,
                iOS: e.indexOf("like Mac OS X") > -1,
                "Chrome OS": e.indexOf("CrOS") > -1,
                WebOS: e.indexOf("hpwOS") > -1,
                Mobile:
                  e.indexOf("Mobi") > -1 ||
                  e.indexOf("iPh") > -1 ||
                  e.indexOf("480") > -1,
                Tablet: e.indexOf("Tablet") > -1 || e.indexOf("Nexus 7") > -1,
                iPad: e.indexOf("iPad") > -1,
              };
            },
            matchInfoMap: function (e) {
              var r = t.navigator.userAgent || {},
                o = n.getMatchMap(r);
              for (var s in t.infoMap)
                for (var i = 0; i < t.infoMap[s].length; i++) {
                  var a = t.infoMap[s][i];
                  o[a] && (e[s] = a);
                }
            },
            getOS: function () {
              return (n.matchInfoMap(this), this.os);
            },
            getOSVersion: function () {
              var e = this,
                n = t.navigator.userAgent || {};
              e.osVersion = "";
              var r = {
                Windows: function () {
                  var e = n.replace(/^.*Windows NT ([\d.]+);.*$/, "$1");
                  return (
                    {
                      10: "10 || 11",
                      6.3: "8.1",
                      6.2: "8",
                      6.1: "7",
                      "6.0": "Vista",
                      5.2: "XP 64-Bit",
                      5.1: "XP",
                      "5.0": "2000",
                      "4.0": "NT 4.0",
                      "3.5.1": "NT 3.5.1",
                      3.5: "NT 3.5",
                      3.1: "NT 3.1",
                    }[e] || e
                  );
                },
                Android: function () {
                  return n.replace(/^.*Android ([\d.]+);.*$/, "$1");
                },
                iOS: function () {
                  return n
                    .replace(/^.*OS ([\d_]+) like.*$/, "$1")
                    .replace(/_/g, ".");
                },
                Debian: function () {
                  return n.replace(/^.*Debian\/([\d.]+).*$/, "$1");
                },
                "Windows Phone": function () {
                  return n.replace(/^.*Windows Phone( OS)? ([\d.]+);.*$/, "$2");
                },
                "Mac OS": function () {
                  return n
                    .replace(/^.*Mac OS X ([\d_]+).*$/, "$1")
                    .replace(/_/g, ".");
                },
                WebOS: function () {
                  return n.replace(/^.*hpwOS\/([\d.]+);.*$/, "$1");
                },
              };
              return (
                r[e.os] &&
                  ((e.osVersion = r[e.os]()),
                  e.osVersion == n && (e.osVersion = "")),
                e.osVersion
              );
            },
            getDeviceType: function () {
              var e = this;
              return ((e.device = "PC"), n.matchInfoMap(e), e.device);
            },
            getNetwork: function () {
              return "";
            },
            getLanguage: function () {
              var e;
              return (
                (this.language =
                  ((e = (
                    t.navigator.browserLanguage || t.navigator.language
                  ).split("-"))[1] && (e[1] = e[1].toUpperCase()),
                  e.join("_"))),
                this.language
              );
            },
            getBrowserInfo: function () {
              var e = this;
              n.matchInfoMap(e);
              var r = t.navigator.userAgent || {},
                o = n.getMatchMap(r);
              if (
                (o.Baidu && o.Opera && (o.Baidu = !1),
                o.Mobile && (o.Mobile = !(r.indexOf("iPad") > -1)),
                o.IE || o.Edge)
              )
                switch (window.screenTop - window.screenY) {
                  case 71:
                  case 74:
                  case 99:
                  case 75:
                  case 74:
                  case 105:
                  default:
                    break;
                  case 102:
                    o["360EE"] = !0;
                    break;
                  case 104:
                    o["360SE"] = !0;
                }
              var s = {
                Safari: function () {
                  return r.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Chrome: function () {
                  return r
                    .replace(/^.*Chrome\/([\d.]+).*$/, "$1")
                    .replace(/^.*CriOS\/([\d.]+).*$/, "$1");
                },
                IE: function () {
                  return r
                    .replace(/^.*MSIE ([\d.]+).*$/, "$1")
                    .replace(/^.*rv:([\d.]+).*$/, "$1");
                },
                Edge: function () {
                  return r.replace(/^.*Edge\/([\d.]+).*$/, "$1");
                },
                Firefox: function () {
                  return r
                    .replace(/^.*Firefox\/([\d.]+).*$/, "$1")
                    .replace(/^.*FxiOS\/([\d.]+).*$/, "$1");
                },
                "Firefox Focus": function () {
                  return r.replace(/^.*Focus\/([\d.]+).*$/, "$1");
                },
                Chromium: function () {
                  return r.replace(/^.*Chromium\/([\d.]+).*$/, "$1");
                },
                Opera: function () {
                  return r
                    .replace(/^.*Opera\/([\d.]+).*$/, "$1")
                    .replace(/^.*OPR\/([\d.]+).*$/, "$1");
                },
                Vivaldi: function () {
                  return r.replace(/^.*Vivaldi\/([\d.]+).*$/, "$1");
                },
                Yandex: function () {
                  return r.replace(/^.*YaBrowser\/([\d.]+).*$/, "$1");
                },
                Arora: function () {
                  return r.replace(/^.*Arora\/([\d.]+).*$/, "$1");
                },
                Lunascape: function () {
                  return r.replace(/^.*Lunascape[\/\s]([\d.]+).*$/, "$1");
                },
                QupZilla: function () {
                  return r.replace(/^.*QupZilla[\/\s]([\d.]+).*$/, "$1");
                },
                "Coc Coc": function () {
                  return r.replace(/^.*coc_coc_browser\/([\d.]+).*$/, "$1");
                },
                Kindle: function () {
                  return r.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Iceweasel: function () {
                  return r.replace(/^.*Iceweasel\/([\d.]+).*$/, "$1");
                },
                Konqueror: function () {
                  return r.replace(/^.*Konqueror\/([\d.]+).*$/, "$1");
                },
                Iceape: function () {
                  return r.replace(/^.*Iceape\/([\d.]+).*$/, "$1");
                },
                SeaMonkey: function () {
                  return r.replace(/^.*SeaMonkey\/([\d.]+).*$/, "$1");
                },
                Epiphany: function () {
                  return r.replace(/^.*Epiphany\/([\d.]+).*$/, "$1");
                },
                Maxthon: function () {
                  return r.replace(/^.*Maxthon\/([\d.]+).*$/, "$1");
                },
              };
              return (
                (e.browserVersion = ""),
                s[e.browser] &&
                  ((e.browserVersion = s[e.browser]()),
                  e.browserVersion == r && (e.browserVersion = "")),
                "Chrome" == e.browser &&
                  r.match(/\S+Browser/) &&
                  ((e.browser = r.match(/\S+Browser/)[0]),
                  (e.version = r.replace(/^.*Browser\/([\d.]+).*$/, "$1"))),
                "Edge" == e.browser &&
                  (e.version > "75"
                    ? (e.engine = "Blink")
                    : (e.engine = "EdgeHTML")),
                (("Chrome" == e.browser && parseInt(e.browserVersion) > 27) ||
                  (o.Chrome &&
                    "WebKit" == e.engine &&
                    parseInt(s.Chrome()) > 27) ||
                  ("Opera" == e.browser && parseInt(e.version) > 12) ||
                  "Yandex" == e.browser) &&
                  (e.engine = "Blink"),
                e.browser +
                  "(version: " +
                  e.browserVersion +
                  "&nbsp;&nbsp;kernel: " +
                  e.engine +
                  ")"
              );
            },
            getGeoPostion: function () {
              return new Promise(function (e, t) {
                navigator && navigator.geolocation
                  ? navigator.geolocation.getCurrentPosition(
                      function (t) {
                        e(t);
                      },
                      function () {
                        e({ coords: { longitude: "fail", latitude: "fail" } });
                      },
                      { enableHighAccuracy: !1, timeout: 1e4 },
                    )
                  : t("fail");
              });
            },
            getPlatform: function () {
              return (
                (t.navigator.userAgentData &&
                  t.navigator.userAgentData.platform) ||
                t.navigator.platform
              );
            },
          },
          r = {
            DeviceInfoObj: function (e) {
              var r = {
                  deviceType: n.getDeviceType(),
                  os: n.getOS(),
                  osVersion: n.getOSVersion(),
                  platform: n.getPlatform(),
                  language: n.getLanguage(),
                  network: n.getNetwork(),
                  browserInfo: n.getBrowserInfo(),
                  userAgent: t.navigator.userAgent,
                  geoPosition: !0,
                  date: n.getDate(),
                  timezoneOffset: n.getTimezoneOffset(),
                  timezone: n.getTimezone(),
                  uuid: n.createUUID(),
                },
                o = {};
              if (e && e.info && 0 !== e.info.length) {
                var s = {},
                  i = function (t) {
                    e.info.forEach(function (e) {
                      e.toLowerCase() === t.toLowerCase() &&
                        (s[(e = t)] = r[e]);
                    });
                  };
                for (var a in r) i(a);
                o = s;
              } else o = r;
              return o;
            },
          };
        return {
          Info: function (e) {
            return r.DeviceInfoObj(e);
          },
        };
      })();
      function T() {
        return M.Info({
          info: [
            "deviceType",
            "OS",
            "OSVersion",
            "platform",
            "language",
            "netWork",
            "browserInfo",
            "screenHeight",
            "screenWidth",
            "userAgent",
            "appCodeName",
            "appName",
            "appVersion",
            "geoPosition",
            "date",
            "UUID",
            "timezoneOffset",
            "timezone",
          ],
        });
      }
      class C {
        static userReg(e, t, n, r, o, s) {
          const i = new FormData();
          let a = T();
          for (const e in a) i.append(e, a[e]);
          (i.append("userId", e),
            i.append("extId", t),
            i.append("version", n),
            i.append("action", r),
            i.append("detail", JSON.stringify(o)),
            console.log(
              "fetch url:https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
            ),
            fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
              { method: "POST", body: i },
            ).then((e) => {
              s && s(e);
            }));
        }
        static logAction(t, n, r, o, s, i, a) {
          return e(this, void 0, void 0, function* () {
            console.log("====start log verion:" + r);
            const e = new FormData();
            let d = T();
            for (const t in d) e.append(t, d[t]);
            (e.append("userId", t),
              e.append("extId", n),
              e.append("version", r),
              e.append("action", o),
              e.append("detail", JSON.stringify(s)),
              i &&
                (e.append("url", i), e.append("domain", new URL(i).hostname)));
            let l = null;
            try {
              let t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/log-action-1",
                { method: "POST", body: e },
              );
              (a && a(t), (l = t.json()));
            } catch (e) {}
            return l;
          });
        }
        static userReg2(t, n, r, o, s, i, a, d) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let l = T();
            for (const t in l) e.append(t, l[t]);
            (e.append("userId", t),
              e.append("extId", n),
              e.append("version", r),
              e.append("action", o),
              e.append("email", s),
              e.append("password", i),
              e.append("emailCode", a),
              e.append("detail", JSON.stringify(d)));
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-2",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              return { success: !0, data: yield t.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static userLogin(t, n, r, o, s, i, a) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let d = T();
            for (const t in d) e.append(t, d[t]);
            (e.append("userId", t),
              e.append("extId", n),
              e.append("version", r),
              e.append("action", o),
              e.append("email", s),
              e.append("password", i),
              e.append("detail", JSON.stringify(a)));
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/login",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              return { success: !0, data: yield t.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static sendEmailCode(t, n, r, o, s, i) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let a = T();
            for (const t in a) e.append(t, a[t]);
            (e.append("userId", t),
              e.append("extId", n),
              e.append("version", r),
              e.append("action", o),
              e.append("email", s),
              e.append("detail", JSON.stringify(i)));
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/getEmailCode",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              return { success: !0, data: yield t.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyPre(t) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            e.append("pssh", t);
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyPre",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              const n = yield t.json();
              return 0 == n.code
                ? { success: !0, data: n.data }
                : { success: !1, error: n.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyAfter(t, n, r, o) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("session_id", t), e.append("licence", n));
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyAfter",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              const n = yield t.json();
              return 0 == n.code
                ? (yield this.saveDrmKey(
                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                    r,
                    n.data.keys.trim(),
                    o,
                  ),
                  { success: !0, data: n.data })
                : { success: !1, error: n.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static checkHasVideo(t, n) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", t), e.append("courseId", n));
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/file/ude/checkHasVideo",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              return { success: !0, data: yield t.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getExtDrmKey(t, n) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", t), e.append("courseId", n));
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getExtDrmKey",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              return { success: !0, data: yield t.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static saveDrmKey(t, n, r, o) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", t),
              e.append("courseId", n),
              e.append("drmKey", r),
              e.append("userId", o));
            try {
              const t = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/saveDrmKey",
                { method: "POST", body: e },
              );
              if (!t.ok) {
                return { success: !1, error: yield t.json() };
              }
              return { success: !0, data: yield t.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getConfig() {
          return e(this, void 0, void 0, function* () {
            let e = "",
              t = !0;
            try {
              const n = new FormData();
              n.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af");
              const r = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getConfig",
                { method: "POST", body: n },
              );
              if (r.ok) {
                e = (yield r.json()).data;
              } else t = !1;
            } catch (e) {
              ((t = !1), console.log(e));
            }
            var n;
            return (
              !t && this.retryCount < this.retryMaxCount
                ? (this.retryCount++,
                  yield ((n = 1e3), new Promise((e) => setTimeout(e, n))),
                  this.getConfig())
                : (this.retryCount = 0),
              e
            );
          });
        }
        static getUserMemberInfo(t, n) {
          return e(this, void 0, void 0, function* () {
            let e = "";
            try {
              const r = new FormData();
              let o = T();
              for (const e in o) r.append(e, o[e]);
              (r.append("userId", t),
                r.append("version", n),
                r.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"));
              const s = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/member/ude/getUserMemberInfoNew",
                { method: "POST", body: r },
              );
              if (s.ok) {
                e = (yield s.json()).data;
              }
            } catch (e) {
              console.log(e);
            }
            return e;
          });
        }
        static getDownloadCount(t) {
          return e(this, void 0, void 0, function* () {
            let e = null;
            try {
              const n = new FormData();
              let r = T();
              for (const e in r) n.append(e, r[e]);
              (n.append("userId", t),
                n.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"));
              const o = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getDownloadCount",
                { method: "POST", body: n },
              );
              if (o.ok) {
                const t = yield o.json();
                0 === t.code && (e = t.data);
              }
            } catch (e) {
              console.log(e);
            }
            return e;
          });
        }
        static updateNoPerDayDownloadCount(t) {
          return e(this, void 0, void 0, function* () {
            try {
              const e = new FormData();
              let n = T();
              for (const t in n) e.append(t, n[t]);
              (e.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
                e.append("detail", t));
              const r = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/member/ude/updateNoPerDayDownloadCount",
                { method: "POST", body: e },
              );
              if (r.ok) {
                0 === (yield r.json()).code &&
                  console.log("update downloadCount success");
              }
            } catch (e) {
              console.log(e);
            }
          });
        }
        static downloadRecord(t, n) {
          return e(this, void 0, void 0, function* () {
            try {
              const e = new FormData();
              let r = T();
              for (const t in r) e.append(t, r[t]);
              (e.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
                e.append("userId", t),
                e.append("version", x().runtime.getManifest().version));
              let o = JSON.stringify({ perday: n });
              e.append("detail", o);
              const s = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/record/ude/downloadRecord",
                { method: "POST", body: e },
              );
              if (s.ok) {
                0 === (yield s.json()).code &&
                  console.log("downloadRecord success");
              }
            } catch (e) {
              console.log(e);
            }
          });
        }
      }
      ((C.retryCount = 0), (C.retryMaxCount = 3));
      const D = "udemyDownloadingInfo",
        P = "udemyDownloadInfo",
        L = "udemyDownloadedInfo",
        $ = 1e3;
      function R(e) {
        return x()
          .storage.local.get(e)
          .then((t) => t[e]);
      }
      function F(e, t) {
        return x().storage.local.set({ [e]: t });
      }
      const U = new (class {
        constructor() {
          this.locks = {};
        }
        acquire(t, n) {
          return e(this, void 0, void 0, function* () {
            const e = (this.locks[t] || Promise.resolve())
              .then(n)
              .catch(() => {});
            return ((this.locks[t] = e), e);
          });
        }
      })();
      function j(t, n, r, o, s) {
        return e(this, void 0, void 0, function* () {
          yield U.acquire(D, () =>
            e(this, void 0, void 0, function* () {
              let e = (yield R(D)) || [],
                i = (yield R(L)) || [];
              const a = e.findIndex((e) => e.downloadId === t);
              if (-1 !== a) {
                const d = e.splice(a, 1)[0];
                (-1 === i.findIndex((e) => e.downloadId === t) &&
                  (i.unshift(
                    Object.assign(Object.assign({}, d), {
                      status: n,
                      downloadMethod: r,
                      msg: o,
                      fileUrl: s,
                      updateTime: new Date().getTime(),
                    }),
                  ),
                  i.length > $ && (i = i.slice(0, $))),
                  yield F(L, i));
              }
              yield F(D, e);
            }),
          );
        });
      }
      function N(t) {
        return e(this, void 0, void 0, function* () {
          let e = yield R(P);
          e || (e = []);
          const n = e.findIndex((e) => e.downloadId === t);
          -1 !== n && (e.splice(n, 1), yield F(P, e));
        });
      }
      function B(e) {
        return "udemy.com,udemybusiness,ssudemy.com,udemyfreecourses.org,discudemy.com,premiumm.click,freecourseudemy.com"
          .split(",")
          .some((t) => e.includes(t));
      }
      function W(t) {
        return e(this, void 0, void 0, function* () {
          let n = t.data;
          try {
            ((n.updateTime = new Date().getTime()),
              t.action === p.addOrUpdateDownloadingInfo
                ? (n = yield (function (t) {
                    return e(this, void 0, void 0, function* () {
                      return (
                        yield U.acquire(D, () =>
                          e(this, void 0, void 0, function* () {
                            let e = (yield R(D)) || [];
                            const n = e.findIndex(
                              (e) => e.downloadId === t.downloadId,
                            );
                            if (-1 !== n)
                              e[n] = Object.assign(Object.assign({}, e[n]), t);
                            else {
                              const n = ((yield R(L)) || []).findIndex(
                                (e) => e.downloadId === t.downloadId,
                              );
                              (yield N(t.downloadId), -1 === n && e.unshift(t));
                            }
                            yield F(D, e);
                          }),
                        ),
                        t
                      );
                    });
                  })(n))
                : t.action === p.updateDownloadStatus &&
                  (yield j(
                    n.downloadId,
                    t.dowloadStatus,
                    n.downloadMethod,
                    t.msg,
                    n.fileUrl,
                  )));
          } catch (e) {
            (console.log(e),
              n.downloadId &&
                (yield j(
                  n.downloadId,
                  f.downloadError,
                  n.downloadMethod,
                  e.toString(),
                )));
          }
        });
      }
      function V(e) {
        if (navigator.appVersion.includes("Win")) {
          const t = /[<>:"\\/|*]/g;
          return e.replace(t, "-").replaceAll("?", "");
        }
        if (navigator.appVersion.includes("Mac")) {
          const t = /[/:]/g;
          return e.replace(t, "");
        }
        return e.replace(/\//g, "");
      }
      const q = {
        au: "audio/basic",
        snd: "audio/basic",
        aif: "audio/x-aiff",
        aifc: "audio/x-aiff",
        aiff: "audio/x-aiff",
        m3u: "audio/x-mpegurl",
        ra: "audio/vnd.rn-realaudio",
        ram: "audio/vnd.rn-realaudio",
        aac: "audio/aac",
        avi: "video/x-msvideo",
        wmv: "video/x-ms-wmv",
        mp3: "audio/mpeg",
        m4a: "audio/mp4",
        mp4: "video/mp4",
        mpeg: "video/mpeg",
        ogg: "audio/ogg",
        ogv: "video/ogg",
        oga: "audio/ogg",
        spx: "audio/ogg",
        ogm: "audio/ogg",
        opus: "audio/opus",
        ts: "video/mp2t",
        wav: "audio/vnd.wav",
        weba: "audio/webm",
        webm: "video/webm",
        "3gp": "video/3gpp",
        "3g2": "video/3gpp2",
        mkv: "video/x-matroska",
      };
      function K(e) {
        return q[
          (function (e) {
            return e.split(".").pop();
          })(e)
        ];
      }
      let Q = null;
      const H =
          (function () {
            const e = navigator.userAgent;
            return e.includes("Edg")
              ? u.Edge
              : e.includes("OPR") || e.includes("Opera")
                ? u.Opera
                : e.includes("Chrome") && !e.includes("Chromium")
                  ? u.Chrome
                  : !e.includes("Safari") ||
                      e.includes("Chrome") ||
                      e.includes("Chromium")
                    ? e.includes("Firefox")
                      ? u.Firefox
                      : u.Unknown
                    : u.Safari;
          })() == u.Firefox,
        z = new k.Z({ concurrency: 1, timeout: 18e4, throwOnTimeout: !0 });
      function G() {
        return e(this, void 0, void 0, function* () {
          (yield (function () {
            return e(this, void 0, void 0, function* () {
              try {
                (Q && Q.loaded && (yield Q.terminate()), (Q = null));
              } catch (e) {
                console.log("FFmpeg termination error:", e);
              }
            });
          })(),
            yield (function () {
              return e(this, void 0, void 0, function* () {
                try {
                  Q = new S();
                  const e = {
                    coreURL: x().runtime.getURL("assets/libs/ffmpeg-core.js"),
                    wasmURL: x().runtime.getURL("assets/libs/ffmpeg-core.wasm"),
                  };
                  (yield Q.load(
                    H
                      ? e
                      : Object.assign(Object.assign({}, e), {
                          workerURL: x().runtime.getURL(
                            "assets/libs/ffmpeg-core.worker.js",
                          ),
                        }),
                  ),
                    console.log("ffmpeg.wasm loaded"));
                } catch (e) {
                  throw (console.log("FFmpeg initialization failed:", e), e);
                }
              });
            })());
        });
      }
      function Z(t) {
        return e(this, void 0, void 0, function* () {
          try {
            return yield Q.exec(t);
          } catch (e) {
            throw (
              console.error("FFmpeg execution error, resetting instance:", e),
              yield G(),
              e
            );
          }
        });
      }
      function X(t, n, r, o, s, i, a) {
        return e(this, void 0, void 0, function* () {
          const d = () =>
            e(this, void 0, void 0, function* () {
              let d = !0,
                l = "";
              const c = t.data.downloadId,
                [u, g, m] = [
                  `${c}-video-${s}.mp4`,
                  `${c}-audio-${s}.m4a`,
                  `${c}-${s}-${n}`,
                ].map(V),
                f = (n) =>
                  e(this, void 0, void 0, function* () {
                    if (
                      ((t.data.percent = parseFloat(
                        (i + n.progress * a).toFixed(2),
                      )),
                      H)
                    )
                      yield W(t);
                    else {
                      let e = x().runtime.connect({ name: "downloadProgress" });
                      (e.postMessage(t), e.disconnect());
                    }
                  });
              try {
                (Q.on("progress", f),
                  yield Promise.all([Q.writeFile(u, r), Q.writeFile(g, o)]),
                  (r = null),
                  (o = null));
                const e = yield Z(["-i", u, "-i", g, "-c:v", "copy", m]);
                if (0 !== e) throw new Error(`Merge failed with code ${e}`);
                l = m;
              } catch (e) {
                throw ((d = !1), console.error("Merge error:", e), e);
              } finally {
                (Q.off("progress", f), J([u, g]));
              }
              return { flag: d, data: l };
            });
          try {
            const e = yield z.add(d);
            if (!e) throw new Error("mergeVideoAndAudio Task execution failed");
            return e;
          } catch (e) {
            throw (console.error("Queue task failed:", e), e);
          }
        });
      }
      function Y(t, n, r) {
        return e(this, void 0, void 0, function* () {
          const o = () =>
            e(this, void 0, void 0, function* () {
              let o = !0,
                s = null;
              const i = t.data.downloadId,
                [a] = [`${i}-${n}`].map(V),
                d = (e) => {
                  console.log(e);
                };
              let l = 98,
                c = 0;
              const u = (n) =>
                e(this, void 0, void 0, function* () {
                  if (((c += 0.01), l < 99.99)) {
                    let e = n.progress;
                    if (
                      (console.log(e),
                      (l = parseFloat((98 + c).toFixed(2))),
                      (t.data.percent = l),
                      H)
                    )
                      yield W(t);
                    else {
                      let e = x().runtime.connect({ name: "downloadProgress" });
                      (e.postMessage(t), e.disconnect());
                    }
                  }
                });
              try {
                (Q.on("log", d), Q.on("progress", u));
                const e = `${i}-filelist.txt`,
                  t = r.map((e) => `file '${e}'`).join("\n");
                yield Q.writeFile(e, t);
                const n = yield Z([
                  "-f",
                  "concat",
                  "-safe",
                  "0",
                  "-i",
                  e,
                  "-c:v",
                  "copy",
                  a,
                ]);
                if (0 !== n) throw new Error(`Concat failed with code ${n}`);
                s = yield Q.readFile(a);
              } catch (e) {
                throw ((o = !1), console.error("Concat error:", e), e);
              } finally {
                (Q.off("progress", u), Q.off("log", d), J([...r, a]));
              }
              return { flag: o, data: s };
            });
          try {
            const e = yield z.add(o);
            if (!e) throw new Error("concatVideo Task execution failed");
            return e;
          } catch (e) {
            throw (console.error("Queue task failed:", e), e);
          }
        });
      }
      function J(e) {
        e.forEach((e) => {
          try {
            null == Q || Q.deleteFile(e);
          } catch (t) {
            console.error(`Delete file error (${e}):`, t);
          }
        });
      }
      (z.on("active", () => {
        console.log(`Task started. Size: ${z.size}, Pending: ${z.pending}`);
      }),
        z.on("idle", () => {
          console.log("Queue is idle. All tasks are done.");
        }));
      let ee = null,
        te = [],
        ne = !1;
      function re() {
        return e(this, void 0, void 0, function* () {
          if (0 === te.length || ne) return;
          ne = !0;
          const {
            inputData: t,
            secretKey: n,
            resolve: r,
            reject: o,
          } = te.shift();
          try {
            let o = yield (function () {
                return e(this, void 0, void 0, function* () {
                  return (null == ee && (ee = yield createModule()), ee);
                });
              })(),
              s = yield (function (t, n, r) {
                return e(this, void 0, void 0, function* () {
                  let e = null,
                    o = null,
                    s = null,
                    i = null;
                  try {
                    (r._main(),
                      (e = r._malloc(t.length)),
                      (o = r._malloc(8)),
                      r.HEAPU8.set(t, e));
                    const a = r._malloc(n.length + 1);
                    (r.stringToUTF8(n, a, n.length + 1),
                      (s = r._test1(e, t.length, o, a)));
                    const d = r.getValue(o, "i32");
                    ((i = new Uint8Array(d)),
                      i.set(r.HEAPU8.subarray(s, s + d)));
                  } finally {
                    (null != o && r._free(o),
                      null != s && r._free(s),
                      null != e && r._free(e),
                      (r = null),
                      (ee = null));
                  }
                  return i;
                });
              })(t, n, o);
            r(s);
          } catch (e) {
            o(e);
          } finally {
            ((ne = !1), setTimeout(re, 0));
          }
        });
      }
      function oe(e, t) {
        return new Promise((n, r) => {
          (te.push({ inputData: e, secretKey: t, resolve: n, reject: r }),
            re());
        });
      }
      var se;
      let ie = (se = class {
        constructor() {
          x().runtime.onConnect.addListener(se.onMessageConnect);
        }
        static isDownloadIdExists(e) {
          return Array.from(se.workersMap.keys()).some((t) => t === e);
        }
        static onMessageConnect(t) {
          return e(this, void 0, void 0, function* () {
            "popupNotification" === t.name &&
              (t.hasPopupNotificationListener ||
                ((t.hasPopupNotificationListener = !0),
                yield t.onMessage.addListener(se.onMessage)));
          });
        }
        static onMessage(t) {
          return e(this, void 0, void 0, function* () {
            t.action === i.download
              ? yield se.startWorkerTask(t)
              : t.action === i.cancle && (yield se.cancleWorkerTask(t));
          });
        }
        static startWorkerTask(t) {
          return e(this, void 0, void 0, function* () {
            if ((se.taskQueue.push(t), !se.isRunning)) {
              for (se.isRunning = !0; se.taskQueue.length > 0; )
                try {
                  const t = se.taskQueue.shift();
                  if (
                    (console.log(se.workersMap),
                    se.isDownloadIdExists(t.workerDownloadMessage.downloadId))
                  )
                    continue;
                  (t.workerDownloadMessage.downloadMethod ==
                    v.processVideoInWeb && (yield G()),
                    console.log("create Worker..."));
                  const n = new Worker(x().runtime.getURL("js/web_worker.js"));
                  (se.workersMap.set(t.workerDownloadMessage.downloadId, n),
                    n.postMessage(t),
                    (n.onmessage = (t) =>
                      e(this, void 0, void 0, function* () {
                        const e = t.data;
                        if (e.action == i.end) {
                          const t = e.endMessage;
                          let r = x().runtime.connect({ name: "downloadFile" });
                          (r.postMessage(t),
                            r.disconnect(),
                            n.terminate(),
                            se.workersMap.delete(t.downloadId));
                        } else if (e.action == i.progress) {
                          const t = e.downloadStorageMessage;
                          yield this.sendDownloadProgress(t);
                        } else if (e.action == i.merge) {
                          let t = e.downloadStorageMessage.data,
                            r = e.downloadStorageMessage.data.downloadId,
                            o = yield se.newMergeVideo(e);
                          if ((n.terminate(), se.workersMap.delete(r), o)) {
                            const t = {
                              downloadId: r,
                              flag: o,
                              errorMsg: "",
                              url: "",
                              filenameOutput:
                                e.workerDownloadMessage.filenameOutput,
                              mediaUrl: e.workerDownloadMessage.mediaUrl,
                            };
                            console.log(t, e);
                            let n = x().runtime.connect({
                              name: "downloadFileInWeb",
                            });
                            (n.postMessage(t), n.disconnect());
                          } else
                            ((t.downloadMethod = v.processVideoInWeb),
                              yield this.sendMessageToRetry(t));
                        } else if (e.action == i.error) {
                          (n.terminate(),
                            se.workersMap.delete(
                              e.downloadStorageMessage.data.downloadId,
                            ));
                          let t = e.downloadStorageMessage.data;
                          ((t.downloadMethod = v.processVideoInWeb),
                            (t.msg = e.downloadStorageMessage.msg),
                            yield this.sendMessageToErrorRetry(t));
                        }
                      })),
                    (n.onerror = (r) =>
                      e(this, void 0, void 0, function* () {
                        console.log("Worker error:", r.message);
                        const e = t.workerDownloadMessage;
                        let o = {
                            hjname: a.downloadToDownloaded,
                            workerDownloadMessage: e,
                            status: f.downloadError,
                            errorMsg: r.message,
                          },
                          s = x().runtime.connect({
                            name: "updateDownloadStatus",
                          });
                        ((s.hasUpdateStatusListener = !0),
                          s.postMessage(o),
                          s.disconnect(),
                          n.terminate(),
                          se.workersMap.delete(e.downloadId));
                      })));
                } catch (e) {
                  console.error("Error processing task:", e);
                }
              se.isRunning = !1;
            }
          });
        }
        static cancleWorkerTask(t) {
          return e(this, void 0, void 0, function* () {
            const e = t.workerDownloadMessage,
              n = e.downloadId,
              r = se.workersMap.get(n);
            r
              ? (r.terminate(),
                se.workersMap.delete(n),
                console.log(`Worker for downloadId ${n} has been canceled.`))
              : console.log(`No worker found for downloadId ${n}.`);
            let o = {
                hjname: a.downloadingToDownloaded,
                workerDownloadMessage: e,
                status: f.downloadCancle,
                errorMsg: "User has cancelled",
              },
              s = x().runtime.connect({ name: "updateDownloadStatus" });
            ((s.hasUpdateStatusListener = !0),
              s.postMessage(o),
              s.disconnect());
          });
        }
        static newMergeVideo(t) {
          return e(this, void 0, void 0, function* () {
            let e = !0;
            const n = t.downloadStorageMessage,
              r = t.workerDownloadMessage;
            let o = t.mergeMessage.videoData,
              s = t.mergeMessage.audioData;
            ((t.mergeMessage.videoData = null),
              (t.mergeMessage.audioData = null),
              (t = null));
            let i = [];
            try {
              const t = o.length;
              let a = 85,
                d = !0,
                l = 13 / t;
              for (let e = 0; e < t; e++) {
                let t = new Uint8Array(o[e]);
                o[e] = null;
                let c = yield oe(t, r.secretKey);
                ((t = null),
                  (n.data.percent = parseFloat((a + (e + 0.4) * l).toFixed(2))),
                  yield this.sendDownloadProgress(n));
                let u = new Uint8Array(s[e]);
                s[e] = null;
                let g = yield oe(u, r.secretKey);
                u = null;
                let m = parseFloat((a + (e + 0.6) * l).toFixed(2));
                ((n.data.percent = m), yield this.sendDownloadProgress(n));
                const f = yield X(n, r.filenameOutput, c, g, e, m, 0.4 * l);
                if (((c = null), (g = null), !f.flag)) {
                  d = !1;
                  break;
                }
                i.push(f.data);
              }
              if (((o = null), (s = null), d)) {
                let t = yield Y(n, r.filenameOutput, i);
                if (t.flag) {
                  const e = this.createBlobUrlFromLargeData(
                      t.data,
                      K(r.filenameOutput),
                    ),
                    o = document.createElement("a");
                  ((o.href = e),
                    (o.download = V(r.filenameOutput)),
                    o.click(),
                    URL.revokeObjectURL(e),
                    (t = null),
                    yield this.sendMessageToMain({
                      action: p.updateDownloadStatus,
                      data: n.data,
                      dowloadStatus: f.downloadSuccess,
                      msg: "",
                    }));
                } else
                  (yield this.sendMessageToMain({
                    action: p.updateDownloadStatus,
                    data: n.data,
                    dowloadStatus: f.downloadError,
                    msg: "video concat failed!",
                  }),
                    (e = !1));
              } else
                (yield this.sendMessageToMain({
                  action: p.updateDownloadStatus,
                  data: n.data,
                  dowloadStatus: f.downloadError,
                  msg: "Audio and video merge failed!",
                }),
                  (e = !1));
            } catch (t) {
              (console.log("Audio and video merge failed:", t),
                yield this.sendMessageToMain({
                  action: p.updateDownloadStatus,
                  data: n.data,
                  dowloadStatus: f.downloadError,
                  msg: "Audio and video merge failed!",
                }),
                (e = !1));
            } finally {
              (o && (o.length = 0), s && (s.length = 0), (n.data = null));
            }
            return e;
          });
        }
        static mergeVideo(t) {
          return e(this, void 0, void 0, function* () {
            let e = !0,
              n = t.downloadStorageMessage,
              r = [],
              o = t.mergeMessage,
              s = t.workerDownloadMessage,
              i = o.videoData,
              a = o.audioData,
              d = [],
              l = [];
            o = null;
            try {
              let t = i.length;
              for (let e = 0; e < t; e++) {
                let r = new Uint8Array(i[e]);
                i[e] = null;
                let o = yield oe(r, s.secretKey);
                (d.push(o),
                  (r = null),
                  (n.data.percent = parseFloat(
                    (85 + ((e + 1) / t) * 3).toFixed(2),
                  )),
                  yield this.sendDownloadProgress(n));
              }
              let o = a.length;
              for (let e = 0; e < o; e++) {
                let r = new Uint8Array(a[e]);
                a[e] = null;
                let o = yield oe(r, s.secretKey);
                (l.push(o),
                  (r = null),
                  (n.data.percent = parseFloat(
                    (88 + ((e + 1) / t) * 2).toFixed(2),
                  )),
                  yield this.sendDownloadProgress(n));
              }
              let c = !0,
                u = 8 / d.length,
                g = 90,
                m = 90;
              for (let e = 0; e < d.length; e++) {
                m = g + u * e;
                let t = yield X(n, s.filenameOutput, d[e], l[e], e, m, u);
                if (((d[e] = null), (l[e] = null), !t.flag)) {
                  c = !1;
                  break;
                }
                (r.push(t.data), (t = null));
              }
              if (((d = null), (l = null), c)) {
                let t = yield Y(n, s.filenameOutput, r);
                if (((r = null), t.flag)) {
                  const e = this.createBlobUrlFromLargeData(
                      t.data,
                      K(s.filenameOutput),
                    ),
                    r = document.createElement("a");
                  ((r.href = e),
                    (r.download = V(s.filenameOutput)),
                    r.click(),
                    URL.revokeObjectURL(e),
                    (t = null),
                    yield this.sendMessageToMain({
                      action: p.updateDownloadStatus,
                      data: n.data,
                      dowloadStatus: f.downloadSuccess,
                      msg: "",
                    }));
                } else
                  (yield this.sendMessageToMain({
                    action: p.updateDownloadStatus,
                    data: n.data,
                    dowloadStatus: f.downloadError,
                    msg: "video concat failed!",
                  }),
                    (e = !1));
              } else
                ((r = null),
                  yield this.sendMessageToMain({
                    action: p.updateDownloadStatus,
                    data: n.data,
                    dowloadStatus: f.downloadError,
                    msg: "Audio and video merge failed!",
                  }),
                  (e = !1));
            } catch (t) {
              (console.log(t),
                yield this.sendMessageToMain({
                  action: p.updateDownloadStatus,
                  data: n.data,
                  dowloadStatus: f.downloadError,
                  msg: "Audio and video merge failed!",
                }),
                (e = !1));
            } finally {
              ((o = null),
                (r = null),
                (n = null),
                (t = null),
                (i = null),
                (a = null),
                (d = null),
                (l = null));
            }
            return e;
          });
        }
        static streamDownload(t, n) {
          return e(this, void 0, void 0, function* () {
            const i = 1048576;
            const a = new ReadableStream({
              start(n) {
                var a, d;
                return e(this, void 0, void 0, function* () {
                  try {
                    try {
                      for (
                        var e,
                          l = s(
                            (function () {
                              return o(this, arguments, function* () {
                                for (let e = 0; e < t.length; e += i) {
                                  const n = t.slice(e, e + i);
                                  yield yield r(n);
                                }
                              });
                            })(),
                          );
                        !(e = yield l.next()).done;

                      ) {
                        const t = e.value;
                        n.enqueue(t);
                      }
                    } catch (e) {
                      a = { error: e };
                    } finally {
                      try {
                        e && !e.done && (d = l.return) && (yield d.call(l));
                      } finally {
                        if (a) throw a.error;
                      }
                    }
                    (n.close(), (t = null), self.gc && self.gc());
                  } catch (e) {
                    n.error(e);
                  }
                });
              },
            });
            return new Response(a, {
              headers: {
                "Content-Type": "application/octet-stream",
                "Content-Disposition": `attachment; filename="${n}"`,
              },
            });
          });
        }
        static productRandom(t, n, r, o, s = 0.1) {
          return e(this, void 0, void 0, function* () {
            let i = 0,
              a = setInterval(function () {
                return e(this, void 0, void 0, function* () {
                  if (i < r) {
                    const e = Math.random() * s;
                    ((i = Math.min(r, i + e)),
                      (n.data.percent =
                        parseFloat(t) + parseFloat(i.toFixed(2))),
                      yield se.sendDownloadProgress(n));
                  } else (clearInterval(a), (a = null));
                });
              }, o);
            return a;
          });
        }
        static sendMessageToMain(t) {
          return e(this, void 0, void 0, function* () {
            let e = x().runtime.connect({ name: "downloadProgress" });
            (e.postMessage(t), e.disconnect());
          });
        }
        static sendMessageToRetry(t) {
          return e(this, void 0, void 0, function* () {
            let e = x().runtime.connect({ name: "retryDownload" });
            (e.postMessage(t), e.disconnect());
          });
        }
        static sendMessageToErrorRetry(t) {
          return e(this, void 0, void 0, function* () {
            let e = x().runtime.connect({ name: "errorRetryDownload" });
            (e.postMessage(t), e.disconnect());
          });
        }
        static sendDownloadProgress(t) {
          return e(this, void 0, void 0, function* () {
            let e = x().runtime.connect({ name: "downloadProgress" });
            (e.postMessage(t), e.disconnect());
          });
        }
        static createBlobUrlFromLargeData(e, t) {
          const n = [];
          let r = 0;
          for (; r < e.byteLength; ) {
            const o = e.slice(r, r + se.CHUNK_SIZE);
            (n.push(new Blob([o], { type: t })), (r += se.CHUNK_SIZE));
          }
          const o = new Blob(n, { type: t });
          return URL.createObjectURL(o);
        }
        static mergeSegments(e) {
          const t = e.reduce((e, t) => e + t.byteLength, 0),
            n = new Uint8Array(t);
          let r = 0;
          return (
            e.forEach((e) => {
              (n.set(new Uint8Array(e), r), (r += e.byteLength));
            }),
            (e = []),
            n
          );
        }
      });
      ((ie.workersMap = new Map()),
        (ie.taskQueue = []),
        (ie.isRunning = !1),
        (ie.CHUNK_SIZE = 104857600),
        (ie = se =
          (function (e, t, n, r) {
            var o,
              s = arguments.length,
              i =
                s < 3
                  ? t
                  : null === r
                    ? (r = Object.getOwnPropertyDescriptor(t, n))
                    : r;
            if (
              "object" == typeof Reflect &&
              "function" == typeof Reflect.decorate
            )
              i = Reflect.decorate(e, t, n, r);
            else
              for (var a = e.length - 1; a >= 0; a--)
                (o = e[a]) &&
                  (i = (s < 3 ? o(i) : s > 3 ? o(t, n, i) : o(t, n)) || i);
            return (s > 3 && i && Object.defineProperty(t, n, i), i);
          })(
            [
              function (e) {
                return new Proxy(e, {
                  construct: (e, t, n) =>
                    e.prototype !== n.prototype
                      ? Reflect.construct(e, t, n)
                      : (e.SINGLETON_INSTANCE ||
                          (e.SINGLETON_INSTANCE = Reflect.construct(e, t, n)),
                        e.SINGLETON_INSTANCE),
                });
              },
            ],
            ie,
          )));
      new ie();
    })());
})();
