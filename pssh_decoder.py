#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PSSH Decoder Tool
فك تشفير وتحليل PSSH (Protection System Specific Header)
"""

import base64
import struct
import binascii
from typing import Dict, List, Optional

class PSShDecoder:
    """أداة فك تشفير PSSH"""
    
    # System IDs المعروفة
    SYSTEM_IDS = {
        "9a04f0799840428eaba2e65be0885f95": "Widevine",
        "edef8ba979d64acea3c827dcd51d21ed": "Widevine (Alternative)",
        "e2719d58a985b3c9781ab030af78d30e": "PlayReady",
        "94ce86fb07ff4f43adb893d2fa968ca2": "FairPlay"
    }
    
    def __init__(self):
        self.results = {}
    
    def decode_base64_pssh(self, base64_pssh: str) -> Dict:
        """فك تشفير PSSH من Base64"""
        try:
            # فك Base64
            pssh_data = base64.b64decode(base64_pssh)
            print(f"📦 حجم البيانات: {len(pssh_data)} بايت")
            
            # تحليل البيانات
            return self.parse_pssh_data(pssh_data)
            
        except Exception as e:
            return {"error": f"خطأ في فك Base64: {str(e)}"}
    
    def parse_pssh_data(self, data: bytes) -> Dict:
        """تحليل بيانات PSSH"""
        results = {"boxes": [], "keys": [], "urls": []}
        offset = 0
        
        while offset < len(data):
            try:
                # قراءة حجم الصندوق
                if offset + 4 > len(data):
                    break
                    
                box_size = struct.unpack(">I", data[offset:offset+4])[0]
                if box_size == 0 or offset + box_size > len(data):
                    break
                
                # قراءة نوع الصندوق
                box_type = data[offset+4:offset+8].decode('ascii', errors='ignore')
                
                print(f"📦 صندوق: {box_type}, الحجم: {box_size}")
                
                if box_type == "pssh":
                    box_data = self.parse_pssh_box(data[offset:offset+box_size])
                    results["boxes"].append(box_data)
                
                offset += box_size
                
            except Exception as e:
                print(f"⚠️ خطأ في التحليل عند الموضع {offset}: {str(e)}")
                break
        
        # البحث عن النصوص المفيدة
        self.extract_text_data(data, results)
        
        return results
    
    def parse_pssh_box(self, box_data: bytes) -> Dict:
        """تحليل صندوق PSSH"""
        result = {"type": "pssh"}
        
        try:
            # تخطي الحجم ونوع الصندوق
            offset = 8
            
            # قراءة الإصدار والأعلام
            if offset + 4 <= len(box_data):
                version_flags = struct.unpack(">I", box_data[offset:offset+4])[0]
                version = (version_flags >> 24) & 0xFF
                result["version"] = version
                offset += 4
            
            # قراءة System ID
            if offset + 16 <= len(box_data):
                system_id = box_data[offset:offset+16]
                system_id_hex = system_id.hex()
                result["system_id"] = system_id_hex
                result["drm_system"] = self.SYSTEM_IDS.get(system_id_hex, "Unknown")
                offset += 16
                
                print(f"🔐 نظام DRM: {result['drm_system']} ({system_id_hex})")
            
            # قراءة Key IDs (إذا كان الإصدار > 0)
            if result.get("version", 0) > 0 and offset + 4 <= len(box_data):
                kid_count = struct.unpack(">I", box_data[offset:offset+4])[0]
                offset += 4
                
                result["key_ids"] = []
                for i in range(kid_count):
                    if offset + 16 <= len(box_data):
                        key_id = box_data[offset:offset+16]
                        key_id_b64 = base64.b64encode(key_id).decode()
                        result["key_ids"].append(key_id_b64)
                        print(f"🔑 Key ID {i+1}: {key_id_b64}")
                        offset += 16
            
            # قراءة البيانات الإضافية
            if offset + 4 <= len(box_data):
                data_size = struct.unpack(">I", box_data[offset:offset+4])[0]
                offset += 4
                
                if offset + data_size <= len(box_data):
                    extra_data = box_data[offset:offset+data_size]
                    result["extra_data"] = self.parse_extra_data(extra_data)
        
        except Exception as e:
            result["error"] = f"خطأ في تحليل PSSH: {str(e)}"
        
        return result
    
    def parse_extra_data(self, data: bytes) -> Dict:
        """تحليل البيانات الإضافية"""
        result = {"raw_size": len(data)}
        
        try:
            # محاولة فك UTF-16 (PlayReady)
            if len(data) > 2:
                try:
                    text = data.decode('utf-16le')
                    if '<WRMHEADER' in text:
                        result["playready_header"] = self.parse_playready_header(text)
                except:
                    pass
            
            # البحث عن URLs
            text_data = data.decode('utf-8', errors='ignore')
            urls = self.extract_urls(text_data)
            if urls:
                result["urls"] = urls
        
        except Exception as e:
            result["error"] = f"خطأ في تحليل البيانات الإضافية: {str(e)}"
        
        return result
    
    def parse_playready_header(self, xml_text: str) -> Dict:
        """تحليل PlayReady Header"""
        result = {}
        
        # استخراج Key ID
        if '<KID>' in xml_text and '</KID>' in xml_text:
            start = xml_text.find('<KID>') + 5
            end = xml_text.find('</KID>')
            result["key_id"] = xml_text[start:end]
        
        # استخراج License URL
        if '<LA_URL>' in xml_text and '</LA_URL>' in xml_text:
            start = xml_text.find('<LA_URL>') + 8
            end = xml_text.find('</LA_URL>')
            result["license_url"] = xml_text[start:end]
        
        # استخراج Algorithm
        if '<ALGID>' in xml_text and '</ALGID>' in xml_text:
            start = xml_text.find('<ALGID>') + 7
            end = xml_text.find('</ALGID>')
            result["algorithm"] = xml_text[start:end]
        
        # استخراج Key Length
        if '<KEYLEN>' in xml_text and '</KEYLEN>' in xml_text:
            start = xml_text.find('<KEYLEN>') + 8
            end = xml_text.find('</KEYLEN>')
            result["key_length"] = xml_text[start:end]
        
        return result
    
    def extract_text_data(self, data: bytes, results: Dict):
        """استخراج النصوص والمعلومات المفيدة"""
        try:
            # محاولة فك UTF-8
            text = data.decode('utf-8', errors='ignore')
            
            # البحث عن URLs
            urls = self.extract_urls(text)
            results["urls"].extend(urls)
            
            # البحث عن Key IDs في النص
            import re
            key_patterns = [
                r'[A-Za-z0-9+/]{22}==',  # Base64 keys
                r'[A-Fa-f0-9]{32}',      # Hex keys
            ]
            
            for pattern in key_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if match not in results["keys"]:
                        results["keys"].append(match)
        
        except Exception as e:
            print(f"⚠️ خطأ في استخراج النصوص: {str(e)}")
    
    def extract_urls(self, text: str) -> List[str]:
        """استخراج URLs من النص"""
        import re
        url_pattern = r'https?://[^\s<>"\']+[^\s<>"\',.]'
        return re.findall(url_pattern, text)
    
    def print_results(self, results: Dict):
        """طباعة النتائج بشكل منظم"""
        print("\n" + "="*60)
        print("🔍 نتائج تحليل PSSH")
        print("="*60)
        
        if "error" in results:
            print(f"❌ خطأ: {results['error']}")
            return
        
        # طباعة الصناديق
        for i, box in enumerate(results.get("boxes", [])):
            print(f"\n📦 الصندوق {i+1}:")
            print(f"   النوع: {box.get('type', 'غير معروف')}")
            print(f"   نظام DRM: {box.get('drm_system', 'غير معروف')}")
            print(f"   System ID: {box.get('system_id', 'غير متوفر')}")
            
            if "key_ids" in box:
                print(f"   Key IDs:")
                for j, kid in enumerate(box["key_ids"]):
                    print(f"     {j+1}. {kid}")
            
            if "extra_data" in box and "playready_header" in box["extra_data"]:
                pr = box["extra_data"]["playready_header"]
                print(f"   PlayReady Info:")
                if "key_id" in pr:
                    print(f"     Key ID: {pr['key_id']}")
                if "license_url" in pr:
                    print(f"     License URL: {pr['license_url']}")
                if "algorithm" in pr:
                    print(f"     Algorithm: {pr['algorithm']}")
                if "key_length" in pr:
                    print(f"     Key Length: {pr['key_length']}")
        
        # طباعة URLs
        if results.get("urls"):
            print(f"\n🌐 URLs المكتشفة:")
            for url in set(results["urls"]):
                print(f"   • {url}")
        
        # طباعة المفاتيح
        if results.get("keys"):
            print(f"\n🔑 المفاتيح المحتملة:")
            for key in set(results["keys"]):
                print(f"   • {key}")

def main():
    """الوظيفة الرئيسية"""
    print("🔓 أداة فك تشفير PSSH")
    print("=" * 40)
    
    # البيانات التي قدمتها
    pssh_data = "AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoDPABXAFIATQBIAEUAQQBEAEUAUgAgAHgAbQBsAG4AcwA9ACIAaAB0AHQAcAA6AC8ALwBzAGMAaABlAG0AYQBzAC4AbQBpAGMAcgBvAHMAbwBmAHQALgBjAG8AbQAvAEQAUgBNAC8AMgAwADAANwAvADAAMwAvAFAAbABhAHkAUgBlAGEAZAB5AEgAZQBhAGQAZQByACIAIAB2AGUAcgBzAGkAbwBuAD0AIgA0AC4AMAAuADAALgAwACIAPgA8AEQAQQBUAEEAPgA8AFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBFAFkATABFAE4APgAxADYAPAAvAEsARQBZAEwARQBOAD4APABBAEwARwBJAEQAPgBBAEUAUwBDAFQAUgA8AC8AQQBMAEcASQBEAD4APAAvAFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBJAEQAPgAvAHIANgBIAEgASAB2AGkAbABFADIAWQBRAGkAKwBEAC8AKwBCAGIAQwBnAD0APQA8AC8ASwBJAEQAPgA8AEwAQQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC4AawBlAHkAbwBzAC4AYwBvAG0ALwBhAHAAaQAvAHYANAAvAGcAZQB0AEwAaQBjAGUAbgBzAGUAPAAvAEwAQQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBWAGwAUgA3AEkAZABzAEkASgBFAHUAUgBkADAANgBMAGEAcQBzADIAagB3AD0APQA8AC8ARABTAF8ASQBEAD4APABDAFUAUwBUAE8ATQBBAFQAVABSAEkAQgBVAFQARQBTACAAeABtAGwAbgBzAD0AIgAiAD4APABDAEkARAA+AC8AcgA2AEgASABIAHYAaQBsAEUAMgBZAFEAaQArAEQALwArAEIAYgBDAGcAPQA9ADwALwBDAEkARAA+ADwARABSAE0AVABZAFAARQA+AHMAbQBvAG8AdABoADwALwBEAFIATQBUAFkAUABFAD4APAAvAEMAVQBTAFQATwBNAEEAVABUAFIASQBCAFUAVABFAFMAPgA8AEMASABFAEMASwBTAFUATQA+AGwAMgBOADAAMQBFAHgAVABaAFUAdwA9ADwALwBDAEgARQBDAEsAUwBVAE0APgA8AC8ARABBAFQAQQA+ADwALwBXAFIATQBIAEUAQQBEAEUAUgA+AAAAAFdwc3NoAAAAAO3vi6l51krOo8gn3NUdIe0AAAA3CAESEByHvv7ie02UmEIvg//gWwoaC2J1eWRybWtleW9zIhAch77+4ntNlJhCL4P/4FsKKgJIRA=="
    
    # إنشاء فاكك التشفير
    decoder = PSShDecoder()
    
    # فك التشفير
    results = decoder.decode_base64_pssh(pssh_data)
    
    # طباعة النتائج
    decoder.print_results(results)

if __name__ == "__main__":
    main()
