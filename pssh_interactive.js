#!/usr/bin/env node
/**
 * PSSH Interactive Decoder
 * أداة تفاعلية لفك تشفير PSSH
 */

const readline = require('readline');

class PSShInteractiveDecoder {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.systemIds = {
            "9a04f0799840428eaba2e65be0885f95": "Widevine",
            "edef8ba979d64acea3c827dcd51d21ed": "Widevine (Alternative)", 
            "e2719d58a985b3c9781ab030af78d30e": "PlayReady",
            "94ce86fb07ff4f43adb893d2fa968ca2": "FairPlay"
        };
    }

    /**
     * طلب إدخال من المستخدم
     */
    async askQuestion(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * فك تشفير PSSH من Base64
     */
    decodePSSH(base64Data) {
        try {
            console.log("\n🔓 جاري فك تشفير PSSH...");
            
            // فك Base64
            const buffer = Buffer.from(base64Data, 'base64');
            console.log(`📦 حجم البيانات: ${buffer.length} بايت`);
            
            // تحليل البيانات
            return this.parsePSSHData(buffer);
            
        } catch (error) {
            return { error: `خطأ في فك Base64: ${error.message}` };
        }
    }

    /**
     * تحليل بيانات PSSH
     */
    parsePSSHData(buffer) {
        const results = { boxes: [], keys: [], urls: [] };
        let offset = 0;

        while (offset < buffer.length) {
            try {
                // قراءة حجم الصندوق
                if (offset + 4 > buffer.length) break;
                
                const boxSize = buffer.readUInt32BE(offset);
                if (boxSize === 0 || offset + boxSize > buffer.length) break;

                // قراءة نوع الصندوق
                const boxType = buffer.slice(offset + 4, offset + 8).toString('ascii');
                console.log(`📦 صندوق: ${boxType}, الحجم: ${boxSize}`);

                if (boxType === 'pssh') {
                    const boxData = this.parsePSSHBox(buffer.slice(offset, offset + boxSize));
                    results.boxes.push(boxData);
                }

                offset += boxSize;

            } catch (error) {
                console.log(`⚠️ خطأ في التحليل عند الموضع ${offset}: ${error.message}`);
                break;
            }
        }

        // استخراج النصوص والمعلومات
        this.extractTextData(buffer, results);
        
        return results;
    }

    /**
     * تحليل صندوق PSSH
     */
    parsePSSHBox(boxBuffer) {
        const result = { type: 'pssh' };
        let offset = 8; // تخطي الحجم ونوع الصندوق

        try {
            // قراءة الإصدار والأعلام
            if (offset + 4 <= boxBuffer.length) {
                const versionFlags = boxBuffer.readUInt32BE(offset);
                const version = (versionFlags >> 24) & 0xFF;
                result.version = version;
                offset += 4;
            }

            // قراءة System ID
            if (offset + 16 <= boxBuffer.length) {
                const systemId = boxBuffer.slice(offset, offset + 16);
                const systemIdHex = systemId.toString('hex');
                result.system_id = systemIdHex;
                result.drm_system = this.systemIds[systemIdHex] || 'Unknown';
                offset += 16;
                
                console.log(`🔐 نظام DRM: ${result.drm_system} (${systemIdHex})`);
            }

            // قراءة Key IDs (إذا كان الإصدار > 0)
            if (result.version > 0 && offset + 4 <= boxBuffer.length) {
                const kidCount = boxBuffer.readUInt32BE(offset);
                offset += 4;

                result.key_ids = [];
                for (let i = 0; i < kidCount; i++) {
                    if (offset + 16 <= boxBuffer.length) {
                        const keyId = boxBuffer.slice(offset, offset + 16);
                        const keyIdB64 = keyId.toString('base64');
                        result.key_ids.push(keyIdB64);
                        console.log(`🔑 Key ID ${i + 1}: ${keyIdB64}`);
                        offset += 16;
                    }
                }
            }

            // قراءة البيانات الإضافية
            if (offset + 4 <= boxBuffer.length) {
                const dataSize = boxBuffer.readUInt32BE(offset);
                offset += 4;

                if (offset + dataSize <= boxBuffer.length) {
                    const extraData = boxBuffer.slice(offset, offset + dataSize);
                    result.extra_data = this.parseExtraData(extraData);
                }
            }

        } catch (error) {
            result.error = `خطأ في تحليل PSSH: ${error.message}`;
        }

        return result;
    }

    /**
     * تحليل البيانات الإضافية
     */
    parseExtraData(buffer) {
        const result = { raw_size: buffer.length };

        try {
            // محاولة فك UTF-16 (PlayReady)
            if (buffer.length > 2) {
                try {
                    const text = buffer.toString('utf16le');
                    if (text.includes('<WRMHEADER')) {
                        result.playready_header = this.parsePlayReadyHeader(text);
                    }
                } catch (e) {
                    // تجاهل الأخطاء
                }
            }

            // البحث عن URLs
            const textData = buffer.toString('utf8').replace(/\0/g, '');
            const urls = this.extractUrls(textData);
            if (urls.length > 0) {
                result.urls = urls;
            }

        } catch (error) {
            result.error = `خطأ في تحليل البيانات الإضافية: ${error.message}`;
        }

        return result;
    }

    /**
     * تحليل PlayReady Header
     */
    parsePlayReadyHeader(xmlText) {
        const result = {};

        // استخراج Key ID
        const kidMatch = xmlText.match(/<KID>([^<]+)<\/KID>/);
        if (kidMatch) result.key_id = kidMatch[1];

        // استخراج License URL
        const urlMatch = xmlText.match(/<LA_URL>([^<]+)<\/LA_URL>/);
        if (urlMatch) result.license_url = urlMatch[1];

        // استخراج Algorithm
        const algMatch = xmlText.match(/<ALGID>([^<]+)<\/ALGID>/);
        if (algMatch) result.algorithm = algMatch[1];

        // استخراج Key Length
        const keyLenMatch = xmlText.match(/<KEYLEN>([^<]+)<\/KEYLEN>/);
        if (keyLenMatch) result.key_length = keyLenMatch[1];

        return result;
    }

    /**
     * استخراج النصوص والمعلومات المفيدة
     */
    extractTextData(buffer, results) {
        try {
            const text = buffer.toString('utf8').replace(/\0/g, '');
            
            // البحث عن URLs
            const urls = this.extractUrls(text);
            results.urls.push(...urls);

            // البحث عن Key IDs في النص
            const keyPatterns = [
                /[A-Za-z0-9+/]{22}==/g,  // Base64 keys
                /[A-Fa-f0-9]{32}/g,      // Hex keys
            ];

            keyPatterns.forEach(pattern => {
                const matches = text.match(pattern) || [];
                matches.forEach(match => {
                    if (!results.keys.includes(match)) {
                        results.keys.push(match);
                    }
                });
            });

        } catch (error) {
            console.log(`⚠️ خطأ في استخراج النصوص: ${error.message}`);
        }
    }

    /**
     * استخراج URLs من النص
     */
    extractUrls(text) {
        const urlPattern = /https?:\/\/[^\s<>"']+[^\s<>"',\.]/g;
        return text.match(urlPattern) || [];
    }

    /**
     * طباعة النتائج بشكل منظم
     */
    printResults(results) {
        console.log("\n" + "=".repeat(60));
        console.log("🔍 نتائج تحليل PSSH");
        console.log("=".repeat(60));

        if (results.error) {
            console.log(`❌ خطأ: ${results.error}`);
            return;
        }

        // طباعة الصناديق
        results.boxes.forEach((box, i) => {
            console.log(`\n📦 الصندوق ${i + 1}:`);
            console.log(`   النوع: ${box.type || 'غير معروف'}`);
            console.log(`   نظام DRM: ${box.drm_system || 'غير معروف'}`);
            console.log(`   System ID: ${box.system_id || 'غير متوفر'}`);

            if (box.key_ids) {
                console.log(`   Key IDs:`);
                box.key_ids.forEach((kid, j) => {
                    console.log(`     ${j + 1}. ${kid}`);
                });
            }

            if (box.extra_data && box.extra_data.playready_header) {
                const pr = box.extra_data.playready_header;
                console.log(`   PlayReady Info:`);
                if (pr.key_id) console.log(`     Key ID: ${pr.key_id}`);
                if (pr.license_url) console.log(`     License URL: ${pr.license_url}`);
                if (pr.algorithm) console.log(`     Algorithm: ${pr.algorithm}`);
                if (pr.key_length) console.log(`     Key Length: ${pr.key_length}`);
            }
        });

        // طباعة URLs
        if (results.urls.length > 0) {
            console.log(`\n🌐 URLs المكتشفة:`);
            [...new Set(results.urls)].forEach(url => {
                console.log(`   • ${url}`);
            });
        }

        // طباعة المفاتيح
        if (results.keys.length > 0) {
            console.log(`\n🔑 المفاتيح المحتملة:`);
            [...new Set(results.keys)].forEach(key => {
                console.log(`   • ${key}`);
            });
        }
    }

    /**
     * الحلقة الرئيسية التفاعلية
     */
    async run() {
        console.log("🔓 أداة فك تشفير PSSH التفاعلية");
        console.log("=".repeat(50));
        console.log("💡 الصق PSSH بتنسيق Base64 واضغط Enter");
        console.log("💡 اكتب 'exit' للخروج");
        console.log("💡 اكتب 'example' لاستخدام المثال السابق");
        
        while (true) {
            try {
                const input = await this.askQuestion("\n🔤 أدخل PSSH: ");
                
                if (input.toLowerCase() === 'exit') {
                    console.log("👋 وداعاً!");
                    break;
                }
                
                if (input.toLowerCase() === 'example') {
                    const examplePSSH = "AAADxHBzc2gAAAAAmgTweZhAQoarkuZb4IhflQAAA6SkAwAAAQABAJoDPABXAFIATQBIAEUAQQBEAEUAUgAgAHgAbQBsAG4AcwA9ACIAaAB0AHQAcAA6AC8ALwBzAGMAaABlAG0AYQBzAC4AbQBpAGMAcgBvAHMAbwBmAHQALgBjAG8AbQAvAEQAUgBNAC8AMgAwADAANwAvADAAMwAvAFAAbABhAHkAUgBlAGEAZAB5AEgAZQBhAGQAZQByACIAIAB2AGUAcgBzAGkAbwBuAD0AIgA0AC4AMAAuADAALgAwACIAPgA8AEQAQQBUAEEAPgA8AFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBFAFkATABFAE4APgAxADYAPAAvAEsARQBZAEwARQBOAD4APABBAEwARwBJAEQAPgBBAEUAUwBDAFQAUgA8AC8AQQBMAEcASQBEAD4APAAvAFAAUgBPAFQARQBDAFQASQBOAEYATwA+ADwASwBJAEQAPgAvAHIANgBIAEgASAB2AGkAbABFADIAWQBRAGkAKwBEAC8AKwBCAGIAQwBnAD0APQA8AC8ASwBJAEQAPgA8AEwAQQBfAFUAUgBMAD4AaAB0AHQAcABzADoALwAvAHAAbABhAHkAcgBlAGEAZAB5AC4AawBlAHkAbwBzAC4AYwBvAG0ALwBhAHAAaQAvAHYANAAvAGcAZQB0AEwAaQBjAGUAbgBzAGUAPAAvAEwAQQBfAFUAUgBMAD4APABEAFMAXwBJAEQAPgBWAGwAUgA3AEkAZABzAEkASgBFAHUAUgBkADAANgBMAGEAcQBzADIAagB3AD0APQA8AC8ARABTAF8ASQBEAD4APABDAFUAUwBUAE8ATQBBAFQAVABSAEkAQgBVAFQARQBTACAAeABtAGwAbgBzAD0AIgAiAD4APABDAEkARAA+AC8AcgA2AEgASABIAHYAaQBsAEUAMgBZAFEAaQArAEQALwArAEIAYgBDAGcAPQA9ADwALwBDAEkARAA+ADwARABSAE0AVABZAFAARQA+AHMAbQBvAG8AdABoADwALwBEAFIATQBUAFkAUABFAD4APAAvAEMAVQBTAFQATwBNAEEAVABUAFIASQBCAFUAVABFAFMAPgA8AEMASABFAEMASwBTAFUATQA+AGwAMgBOADAAMQBFAHgAVABaAFUAdwA9ADwALwBDAEgARQBDAEsAUwBVAE0APgA8AC8ARABBAFQAQQA+ADwALwBXAFIATQBIAEUAQQBEAEUAUgA+AAAAAFdwc3NoAAAAAO3vi6l51krOo8gn3NUdIe0AAAA3CAESEByHvv7ie02UmEIvg//gWwoaC2J1eWRybWtleW9zIhAch77+4ntNlJhCL4P/4FsKKgJIRA==";
                    console.log("📝 استخدام المثال السابق...");
                    const results = this.decodePSSH(examplePSSH);
                    this.printResults(results);
                    continue;
                }
                
                if (!input) {
                    console.log("⚠️ يرجى إدخال PSSH صحيح");
                    continue;
                }
                
                // فك تشفير PSSH
                const results = this.decodePSSH(input);
                this.printResults(results);
                
            } catch (error) {
                console.error(`❌ خطأ: ${error.message}`);
            }
        }
        
        this.rl.close();
    }
}

// تشغيل الأداة
if (require.main === module) {
    const decoder = new PSShInteractiveDecoder();
    decoder.run().catch(console.error);
}

module.exports = PSShInteractiveDecoder;
