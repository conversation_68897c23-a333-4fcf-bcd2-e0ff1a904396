(()=>{"use strict";var e={"./node_modules/@ffmpeg/ffmpeg/dist/esm/const.js":(e,s,t)=>{t.r(s),t.d(s,{MIME_TYPE_JAVASCRIPT:()=>r,MIME_TYPE_WASM:()=>a,CORE_VERSION:()=>o,CORE_URL:()=>E,FFMessageType:()=>p});const r="text/javascript",a="application/wasm",o="0.12.6",E=`https://unpkg.com/@ffmpeg/core@${o}/dist/umd/ffmpeg-core.js`;var p;!function(e){e.LOAD="LOAD",e.EXEC="EXEC",e.WRITE_FILE="WRITE_FILE",e.READ_FILE="READ_FILE",e.DELETE_FILE="DELETE_FILE",e.RENAME="RENAME",e.CREATE_DIR="CREATE_DIR",e.LIST_DIR="LIST_DIR",e.DELETE_DIR="DELETE_DIR",e.ERROR="ERROR",e.DOWNLOAD="DOWNLOAD",e.PROGRESS="PROGRESS",e.LOG="LOG",e.MOUNT="MOUNT",e.UNMOUNT="UNMOUNT"}(p||(p={}))},"./node_modules/@ffmpeg/ffmpeg/dist/esm/errors.js":(e,s,t)=>{t.r(s),t.d(s,{ERROR_UNKNOWN_MESSAGE_TYPE:()=>r,ERROR_NOT_LOADED:()=>a,ERROR_TERMINATED:()=>o,ERROR_IMPORT_FAILURE:()=>E});const r=new Error("unknown message type"),a=new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first"),o=new Error("called FFmpeg.terminate()"),E=new Error("failed to import ffmpeg-core.js")}},s={};function t(r){var a=s[r];if(void 0!==a)return a.exports;var o=s[r]={exports:{}};return e[r](o,o.exports,t),o.exports}t.d=(e,s)=>{for(var r in s)t.o(s,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:s[r]})},t.o=(e,s)=>Object.prototype.hasOwnProperty.call(e,s),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{t.r(r);var e=t("./node_modules/@ffmpeg/ffmpeg/dist/esm/const.js"),s=t("./node_modules/@ffmpeg/ffmpeg/dist/esm/errors.js");let a;self.onmessage=async({data:{id:t,type:r,data:o}})=>{const E=[];let p;try{if(r!==e.FFMessageType.LOAD&&!a)throw s.ERROR_NOT_LOADED;switch(r){case e.FFMessageType.LOAD:p=await(async({coreURL:t,wasmURL:r,workerURL:o})=>{const E=!a;try{t||(t=e.CORE_URL),importScripts(t)}catch{if(t||(t=e.CORE_URL.replace("/umd/","/esm/")),self.createFFmpegCore=(await import(t)).default,!self.createFFmpegCore)throw s.ERROR_IMPORT_FAILURE}const p=t,R=r||t.replace(/.js$/g,".wasm"),n=o||t.replace(/.js$/g,".worker.js");return a=await self.createFFmpegCore({mainScriptUrlOrBlob:`${p}#${btoa(JSON.stringify({wasmURL:R,workerURL:n}))}`}),a.setLogger((s=>self.postMessage({type:e.FFMessageType.LOG,data:s}))),a.setProgress((s=>self.postMessage({type:e.FFMessageType.PROGRESS,data:s}))),E})(o);break;case e.FFMessageType.EXEC:p=(({args:e,timeout:s=-1})=>{a.setTimeout(s),a.exec(...e);const t=a.ret;return a.reset(),t})(o);break;case e.FFMessageType.WRITE_FILE:p=(({path:e,data:s})=>(a.FS.writeFile(e,s),!0))(o);break;case e.FFMessageType.READ_FILE:p=(({path:e,encoding:s})=>a.FS.readFile(e,{encoding:s}))(o);break;case e.FFMessageType.DELETE_FILE:p=(({path:e})=>(a.FS.unlink(e),!0))(o);break;case e.FFMessageType.RENAME:p=(({oldPath:e,newPath:s})=>(a.FS.rename(e,s),!0))(o);break;case e.FFMessageType.CREATE_DIR:p=(({path:e})=>(a.FS.mkdir(e),!0))(o);break;case e.FFMessageType.LIST_DIR:p=(({path:e})=>{const s=a.FS.readdir(e),t=[];for(const r of s){const s=a.FS.stat(`${e}/${r}`),o=a.FS.isDir(s.mode);t.push({name:r,isDir:o})}return t})(o);break;case e.FFMessageType.DELETE_DIR:p=(({path:e})=>(a.FS.rmdir(e),!0))(o);break;case e.FFMessageType.MOUNT:p=(({fsType:e,options:s,mountPoint:t})=>{const r=e,o=a.FS.filesystems[r];return!!o&&(a.FS.mount(o,s,t),!0)})(o);break;case e.FFMessageType.UNMOUNT:p=(({mountPoint:e})=>(a.FS.unmount(e),!0))(o);break;default:throw s.ERROR_UNKNOWN_MESSAGE_TYPE}}catch(s){return void self.postMessage({id:t,type:e.FFMessageType.ERROR,data:s.toString()})}p instanceof Uint8Array&&E.push(p.buffer),self.postMessage({id:t,type:r,data:p},E)}})()})();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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