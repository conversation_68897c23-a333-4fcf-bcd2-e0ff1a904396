
.alert-wrapper {
  position: fixed;
  bottom: 4rem;
  right: 1rem;
  width: calc(80vw - 1rem);
  z-index: 2000;
  max-width: 550px;
}


.alert {
  opacity: 0;
  transition: opacity .2s;
  position: relative;

  margin-top: 20px;
  padding: 15px 29px 15px 15px;

  animation: fade-in .3s;
  border-radius: 5px;

  border: solid 3px;
  background-color: white;
  color: rgb(38, 38, 38);

  &.fade-in {
    opacity: 1;
  }

  &.fade-out {
    opacity: 0;
  }
}

.close {
  user-select: none;
  position: absolute;
  content: var(--extension-close-icon);
  cursor: pointer;
  right: 5px;
  top: 5px;
}

.default {
  border-color: rgba(105, 187, 255, 0.5);
}

.warn {
  border-color: rgba(255, 191, 100, 0.5);
}

.error {
  border-color: rgba(255, 105, 79, 0.5);
}
