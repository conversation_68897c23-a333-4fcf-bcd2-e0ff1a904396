(() => {
  var e = {
      150: function (e, n) {
        var t, r, o;
        ("undefined" != typeof globalThis
          ? globalThis
          : "undefined" != typeof self && self,
          (r = [e]),
          (t = function (e) {
            "use strict";
            if (
              !(
                globalThis.chrome &&
                globalThis.chrome.runtime &&
                globalThis.chrome.runtime.id
              )
            )
              throw new Error(
                "This script should only be loaded in a browser extension.",
              );
            if (
              globalThis.browser &&
              globalThis.browser.runtime &&
              globalThis.browser.runtime.id
            )
              e.exports = globalThis.browser;
            else {
              const n =
                  "The message port closed before a response was received.",
                t = (e) => {
                  const t = {
                    alarms: {
                      clear: { minArgs: 0, maxArgs: 1 },
                      clearAll: { minArgs: 0, maxArgs: 0 },
                      get: { minArgs: 0, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                    },
                    bookmarks: {
                      create: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getChildren: { minArgs: 1, maxArgs: 1 },
                      getRecent: { minArgs: 1, maxArgs: 1 },
                      getSubTree: { minArgs: 1, maxArgs: 1 },
                      getTree: { minArgs: 0, maxArgs: 0 },
                      move: { minArgs: 2, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeTree: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    browserAction: {
                      disable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      enable: {
                        minArgs: 0,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      getBadgeBackgroundColor: { minArgs: 1, maxArgs: 1 },
                      getBadgeText: { minArgs: 1, maxArgs: 1 },
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      openPopup: { minArgs: 0, maxArgs: 0 },
                      setBadgeBackgroundColor: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setBadgeText: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    browsingData: {
                      remove: { minArgs: 2, maxArgs: 2 },
                      removeCache: { minArgs: 1, maxArgs: 1 },
                      removeCookies: { minArgs: 1, maxArgs: 1 },
                      removeDownloads: { minArgs: 1, maxArgs: 1 },
                      removeFormData: { minArgs: 1, maxArgs: 1 },
                      removeHistory: { minArgs: 1, maxArgs: 1 },
                      removeLocalStorage: { minArgs: 1, maxArgs: 1 },
                      removePasswords: { minArgs: 1, maxArgs: 1 },
                      removePluginData: { minArgs: 1, maxArgs: 1 },
                      settings: { minArgs: 0, maxArgs: 0 },
                    },
                    commands: { getAll: { minArgs: 0, maxArgs: 0 } },
                    contextMenus: {
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeAll: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    cookies: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 1, maxArgs: 1 },
                      getAllCookieStores: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    },
                    devtools: {
                      inspectedWindow: {
                        eval: { minArgs: 1, maxArgs: 2, singleCallbackArg: !1 },
                      },
                      panels: {
                        create: {
                          minArgs: 3,
                          maxArgs: 3,
                          singleCallbackArg: !0,
                        },
                        elements: {
                          createSidebarPane: { minArgs: 1, maxArgs: 1 },
                        },
                      },
                    },
                    downloads: {
                      cancel: { minArgs: 1, maxArgs: 1 },
                      download: { minArgs: 1, maxArgs: 1 },
                      erase: { minArgs: 1, maxArgs: 1 },
                      getFileIcon: { minArgs: 1, maxArgs: 2 },
                      open: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      pause: { minArgs: 1, maxArgs: 1 },
                      removeFile: { minArgs: 1, maxArgs: 1 },
                      resume: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    extension: {
                      isAllowedFileSchemeAccess: { minArgs: 0, maxArgs: 0 },
                      isAllowedIncognitoAccess: { minArgs: 0, maxArgs: 0 },
                    },
                    history: {
                      addUrl: { minArgs: 1, maxArgs: 1 },
                      deleteAll: { minArgs: 0, maxArgs: 0 },
                      deleteRange: { minArgs: 1, maxArgs: 1 },
                      deleteUrl: { minArgs: 1, maxArgs: 1 },
                      getVisits: { minArgs: 1, maxArgs: 1 },
                      search: { minArgs: 1, maxArgs: 1 },
                    },
                    i18n: {
                      detectLanguage: { minArgs: 1, maxArgs: 1 },
                      getAcceptLanguages: { minArgs: 0, maxArgs: 0 },
                    },
                    identity: { launchWebAuthFlow: { minArgs: 1, maxArgs: 1 } },
                    idle: { queryState: { minArgs: 1, maxArgs: 1 } },
                    management: {
                      get: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getSelf: { minArgs: 0, maxArgs: 0 },
                      setEnabled: { minArgs: 2, maxArgs: 2 },
                      uninstallSelf: { minArgs: 0, maxArgs: 1 },
                    },
                    notifications: {
                      clear: { minArgs: 1, maxArgs: 1 },
                      create: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      getPermissionLevel: { minArgs: 0, maxArgs: 0 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                    pageAction: {
                      getPopup: { minArgs: 1, maxArgs: 1 },
                      getTitle: { minArgs: 1, maxArgs: 1 },
                      hide: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setIcon: { minArgs: 1, maxArgs: 1 },
                      setPopup: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      setTitle: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                      show: {
                        minArgs: 1,
                        maxArgs: 1,
                        fallbackToNoCallback: !0,
                      },
                    },
                    permissions: {
                      contains: { minArgs: 1, maxArgs: 1 },
                      getAll: { minArgs: 0, maxArgs: 0 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      request: { minArgs: 1, maxArgs: 1 },
                    },
                    runtime: {
                      getBackgroundPage: { minArgs: 0, maxArgs: 0 },
                      getPlatformInfo: { minArgs: 0, maxArgs: 0 },
                      openOptionsPage: { minArgs: 0, maxArgs: 0 },
                      requestUpdateCheck: { minArgs: 0, maxArgs: 0 },
                      sendMessage: { minArgs: 1, maxArgs: 3 },
                      sendNativeMessage: { minArgs: 2, maxArgs: 2 },
                      setUninstallURL: { minArgs: 1, maxArgs: 1 },
                    },
                    sessions: {
                      getDevices: { minArgs: 0, maxArgs: 1 },
                      getRecentlyClosed: { minArgs: 0, maxArgs: 1 },
                      restore: { minArgs: 0, maxArgs: 1 },
                    },
                    storage: {
                      local: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                      managed: {
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                      },
                      sync: {
                        clear: { minArgs: 0, maxArgs: 0 },
                        get: { minArgs: 0, maxArgs: 1 },
                        getBytesInUse: { minArgs: 0, maxArgs: 1 },
                        remove: { minArgs: 1, maxArgs: 1 },
                        set: { minArgs: 1, maxArgs: 1 },
                      },
                    },
                    tabs: {
                      captureVisibleTab: { minArgs: 0, maxArgs: 2 },
                      create: { minArgs: 1, maxArgs: 1 },
                      detectLanguage: { minArgs: 0, maxArgs: 1 },
                      discard: { minArgs: 0, maxArgs: 1 },
                      duplicate: { minArgs: 1, maxArgs: 1 },
                      executeScript: { minArgs: 1, maxArgs: 2 },
                      get: { minArgs: 1, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 0 },
                      getZoom: { minArgs: 0, maxArgs: 1 },
                      getZoomSettings: { minArgs: 0, maxArgs: 1 },
                      goBack: { minArgs: 0, maxArgs: 1 },
                      goForward: { minArgs: 0, maxArgs: 1 },
                      highlight: { minArgs: 1, maxArgs: 1 },
                      insertCSS: { minArgs: 1, maxArgs: 2 },
                      move: { minArgs: 2, maxArgs: 2 },
                      query: { minArgs: 1, maxArgs: 1 },
                      reload: { minArgs: 0, maxArgs: 2 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      removeCSS: { minArgs: 1, maxArgs: 2 },
                      sendMessage: { minArgs: 2, maxArgs: 3 },
                      setZoom: { minArgs: 1, maxArgs: 2 },
                      setZoomSettings: { minArgs: 1, maxArgs: 2 },
                      update: { minArgs: 1, maxArgs: 2 },
                    },
                    topSites: { get: { minArgs: 0, maxArgs: 0 } },
                    webNavigation: {
                      getAllFrames: { minArgs: 1, maxArgs: 1 },
                      getFrame: { minArgs: 1, maxArgs: 1 },
                    },
                    webRequest: {
                      handlerBehaviorChanged: { minArgs: 0, maxArgs: 0 },
                    },
                    windows: {
                      create: { minArgs: 0, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 2 },
                      getAll: { minArgs: 0, maxArgs: 1 },
                      getCurrent: { minArgs: 0, maxArgs: 1 },
                      getLastFocused: { minArgs: 0, maxArgs: 1 },
                      remove: { minArgs: 1, maxArgs: 1 },
                      update: { minArgs: 2, maxArgs: 2 },
                    },
                  };
                  if (0 === Object.keys(t).length)
                    throw new Error(
                      "api-metadata.json has not been included in browser-polyfill",
                    );
                  class r extends WeakMap {
                    constructor(e, n = void 0) {
                      (super(n), (this.createItem = e));
                    }
                    get(e) {
                      return (
                        this.has(e) || this.set(e, this.createItem(e)),
                        super.get(e)
                      );
                    }
                  }
                  const o = (e) =>
                      e && "object" == typeof e && "function" == typeof e.then,
                    s =
                      (n, t) =>
                      (...r) => {
                        e.runtime.lastError
                          ? n.reject(new Error(e.runtime.lastError.message))
                          : t.singleCallbackArg ||
                              (r.length <= 1 && !1 !== t.singleCallbackArg)
                            ? n.resolve(r[0])
                            : n.resolve(r);
                      },
                    a = (e) => (1 == e ? "argument" : "arguments"),
                    i = (e, n) =>
                      function (t, ...r) {
                        if (r.length < n.minArgs)
                          throw new Error(
                            `Expected at least ${n.minArgs} ${a(n.minArgs)} for ${e}(), got ${r.length}`,
                          );
                        if (r.length > n.maxArgs)
                          throw new Error(
                            `Expected at most ${n.maxArgs} ${a(n.maxArgs)} for ${e}(), got ${r.length}`,
                          );
                        return new Promise((o, a) => {
                          if (n.fallbackToNoCallback)
                            try {
                              t[e](...r, s({ resolve: o, reject: a }, n));
                            } catch (s) {
                              (console.warn(
                                `${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,
                                s,
                              ),
                                t[e](...r),
                                (n.fallbackToNoCallback = !1),
                                (n.noCallback = !0),
                                o());
                            }
                          else
                            n.noCallback
                              ? (t[e](...r), o())
                              : t[e](...r, s({ resolve: o, reject: a }, n));
                        });
                      },
                    d = (e, n, t) =>
                      new Proxy(n, { apply: (n, r, o) => t.call(r, e, ...o) });
                  let l = Function.call.bind(Object.prototype.hasOwnProperty);
                  const c = (e, n = {}, t = {}) => {
                      let r = Object.create(null),
                        o = {
                          has: (n, t) => t in e || t in r,
                          get(o, s, a) {
                            if (s in r) return r[s];
                            if (!(s in e)) return;
                            let m = e[s];
                            if ("function" == typeof m)
                              if ("function" == typeof n[s])
                                m = d(e, e[s], n[s]);
                              else if (l(t, s)) {
                                let n = i(s, t[s]);
                                m = d(e, e[s], n);
                              } else m = m.bind(e);
                            else if (
                              "object" == typeof m &&
                              null !== m &&
                              (l(n, s) || l(t, s))
                            )
                              m = c(m, n[s], t[s]);
                            else {
                              if (!l(t, "*"))
                                return (
                                  Object.defineProperty(r, s, {
                                    configurable: !0,
                                    enumerable: !0,
                                    get: () => e[s],
                                    set(n) {
                                      e[s] = n;
                                    },
                                  }),
                                  m
                                );
                              m = c(m, n[s], t["*"]);
                            }
                            return ((r[s] = m), m);
                          },
                          set: (n, t, o, s) => (
                            t in r ? (r[t] = o) : (e[t] = o),
                            !0
                          ),
                          defineProperty: (e, n, t) =>
                            Reflect.defineProperty(r, n, t),
                          deleteProperty: (e, n) =>
                            Reflect.deleteProperty(r, n),
                        },
                        s = Object.create(e);
                      return new Proxy(s, o);
                    },
                    m = (e) => ({
                      addListener(n, t, ...r) {
                        n.addListener(e.get(t), ...r);
                      },
                      hasListener: (n, t) => n.hasListener(e.get(t)),
                      removeListener(n, t) {
                        n.removeListener(e.get(t));
                      },
                    }),
                    g = new r((e) =>
                      "function" != typeof e
                        ? e
                        : function (n) {
                            const t = c(
                              n,
                              {},
                              { getContent: { minArgs: 0, maxArgs: 0 } },
                            );
                            e(t);
                          },
                    ),
                    u = new r((e) =>
                      "function" != typeof e
                        ? e
                        : function (n, t, r) {
                            let s,
                              a,
                              i = !1,
                              d = new Promise((e) => {
                                s = function (n) {
                                  ((i = !0), e(n));
                                };
                              });
                            try {
                              a = e(n, t, s);
                            } catch (e) {
                              a = Promise.reject(e);
                            }
                            const l = !0 !== a && o(a);
                            if (!0 !== a && !l && !i) return !1;
                            const c = (e) => {
                              e.then(
                                (e) => {
                                  r(e);
                                },
                                (e) => {
                                  let n;
                                  ((n =
                                    e &&
                                    (e instanceof Error ||
                                      "string" == typeof e.message)
                                      ? e.message
                                      : "An unexpected error occurred"),
                                    r({
                                      __mozWebExtensionPolyfillReject__: !0,
                                      message: n,
                                    }));
                                },
                              ).catch((e) => {
                                console.error(
                                  "Failed to send onMessage rejected reply",
                                  e,
                                );
                              });
                            };
                            return (c(l ? a : d), !0);
                          },
                    ),
                    f = ({ reject: t, resolve: r }, o) => {
                      e.runtime.lastError
                        ? e.runtime.lastError.message === n
                          ? r()
                          : t(new Error(e.runtime.lastError.message))
                        : o && o.__mozWebExtensionPolyfillReject__
                          ? t(new Error(o.message))
                          : r(o);
                    },
                    p = (e, n, t, ...r) => {
                      if (r.length < n.minArgs)
                        throw new Error(
                          `Expected at least ${n.minArgs} ${a(n.minArgs)} for ${e}(), got ${r.length}`,
                        );
                      if (r.length > n.maxArgs)
                        throw new Error(
                          `Expected at most ${n.maxArgs} ${a(n.maxArgs)} for ${e}(), got ${r.length}`,
                        );
                      return new Promise((e, n) => {
                        const o = f.bind(null, { resolve: e, reject: n });
                        (r.push(o), t.sendMessage(...r));
                      });
                    },
                    A = {
                      devtools: { network: { onRequestFinished: m(g) } },
                      runtime: {
                        onMessage: m(u),
                        onMessageExternal: m(u),
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 1,
                          maxArgs: 3,
                        }),
                      },
                      tabs: {
                        sendMessage: p.bind(null, "sendMessage", {
                          minArgs: 2,
                          maxArgs: 3,
                        }),
                      },
                    },
                    x = {
                      clear: { minArgs: 1, maxArgs: 1 },
                      get: { minArgs: 1, maxArgs: 1 },
                      set: { minArgs: 1, maxArgs: 1 },
                    };
                  return (
                    (t.privacy = {
                      network: { "*": x },
                      services: { "*": x },
                      websites: { "*": x },
                    }),
                    c(e, A, t)
                  );
                };
              e.exports = t(chrome);
            }
          }),
          void 0 === (o = "function" == typeof t ? t.apply(n, r) : t) ||
            (e.exports = o));
      },
    },
    n = {};
  function t(r) {
    var o = n[r];
    if (void 0 !== o) return o.exports;
    var s = (n[r] = { exports: {} });
    return (e[r].call(s.exports, s, s.exports, t), s.exports);
  }
  ((t.n = (e) => {
    var n = e && e.__esModule ? () => e.default : () => e;
    return (t.d(n, { a: n }), n);
  }),
    (t.d = (e, n) => {
      for (var r in n)
        t.o(n, r) &&
          !t.o(e, r) &&
          Object.defineProperty(e, r, { enumerable: !0, get: n[r] });
    }),
    (t.o = (e, n) => Object.prototype.hasOwnProperty.call(e, n)),
    (() => {
      "use strict";
      function e(e, n, t, r) {
        return new (t || (t = Promise))(function (o, s) {
          function a(e) {
            try {
              d(r.next(e));
            } catch (e) {
              s(e);
            }
          }
          function i(e) {
            try {
              d(r.throw(e));
            } catch (e) {
              s(e);
            }
          }
          function d(e) {
            var n;
            e.done
              ? o(e.value)
              : ((n = e.value),
                n instanceof t
                  ? n
                  : new t(function (e) {
                      e(n);
                    })).then(a, i);
          }
          d((r = r.apply(e, n || [])).next());
        });
      }
      Object.create;
      Object.create;
      var n,
        r,
        o,
        s,
        a,
        i,
        d,
        l,
        c,
        m,
        g,
        u,
        f = t(150),
        p = t.n(f);
      (!(function (e) {
        ((e[(e.pre = 0)] = "pre"),
          (e[(e.after = 1)] = "after"),
          (e[(e.getExtDrmKey = 2)] = "getExtDrmKey"));
      })(n || (n = {})),
        (function (e) {
          ((e[(e.single = 0)] = "single"),
            (e[(e.bulk = 1)] = "bulk"),
            (e[(e.bloburl = 2)] = "bloburl"),
            (e[(e.changeUrl = 3)] = "changeUrl"),
            (e[(e.login = 4)] = "login"),
            (e[(e.googleLogin = 5)] = "googleLogin"),
            (e[(e.register = 6)] = "register"),
            (e[(e.sendEmailCode = 7)] = "sendEmailCode"),
            (e[(e.getDrmSecretKey = 8)] = "getDrmSecretKey"),
            (e[(e.getConfig = 9)] = "getConfig"),
            (e[(e.getMemberInfo = 10)] = "getMemberInfo"),
            (e[(e.updateNoPerDayDownloadCount = 11)] =
              "updateNoPerDayDownloadCount"));
        })(r || (r = {})),
        (function (e) {
          ((e[(e.goSubscribe = 0)] = "goSubscribe"),
            (e[(e.pureNotice = 1)] = "pureNotice"),
            (e[(e.drmLicense = 2)] = "drmLicense"),
            (e[(e.retryMessage = 3)] = "retryMessage"),
            (e[(e.serverError = 4)] = "serverError"));
        })(o || (o = {})),
        (function (e) {
          ((e[(e.Edge = 0)] = "Edge"),
            (e[(e.Chrome = 1)] = "Chrome"),
            (e[(e.Firefox = 2)] = "Firefox"),
            (e[(e.Opera = 3)] = "Opera"),
            (e[(e.Safari = 4)] = "Safari"),
            (e[(e.Unknown = 5)] = "Unknown"));
        })(s || (s = {})),
        (function (e) {
          ((e.default = "log"), (e.warn = "warn"), (e.error = "error"));
        })(a || (a = {})),
        (function (e) {
          ((e.install = "install"),
            (e.uninstall = "uninstall"),
            (e.downloadSignalUnkown = "downloadSignalUnkown"),
            (e.downloadSignalImg = "downloadSignalImg"),
            (e.downloadSignalVideo = "downloadSignalVideo"),
            (e.downloadBulk = "downloadBulk"),
            (e.changeUrl = "changeUrl"),
            (e.register = "register"),
            (e.login = "login"),
            (e.googleLogin = "googleLogin"),
            (e.sendEmailCode = "sendEmailCode"),
            (e.uploadFiles = "uploadFiles"),
            (e.concatVideoAndAudio = "concatVideoAndAudio"));
        })(i || (i = {})),
        (function (e) {
          ((e.downloadSuccess = "downloadSuccess"),
            (e.downloadError = "downloadError"),
            (e.downloadCancle = "downloadCancle"),
            (e.downloadWating = "downloadWating"),
            (e.downloadPrepare = "downloadPrepare"),
            (e.downloadStuck = "downloadStuck"));
        })(d || (d = {})),
        (function (e) {
          ((e.addOrUpdateDownloadingInfo = "addOrUpdateDownloadingInfo"),
            (e.updateDownloadStatus = "updateDownloadStatus"));
        })(l || (l = {})),
        (function (e) {
          e[(e.refresh = 0)] = "refresh";
        })(c || (c = {})),
        (function (e) {
          ((e.downloading = "downloading"),
            (e.downloaded = "downloaded"),
            (e.download = "download"),
            (e.all = "all"),
            (e.quota = "quota"));
        })(m || (m = {})),
        (function (e) {
          ((e.processVideo = "processVideo"),
            (e.processVideoInWeb = "processVideoInWeb"),
            (e.processVideoByUrl = "processVideoByUrl"));
        })(g || (g = {})),
        (function (e) {
          ((e.serverError = "serverError"), (e.tip = "tip"));
        })(u || (u = {})));
      var A;
      !(function (n) {
        let t;
        const r = () => {
          var e;
          null === (e = document.getElementById("addOnInfoWrapperid")) ||
            void 0 === e ||
            e.remove();
          const n = document.createElement("div");
          return (
            (n.id = "addOnInfoWrapperid"),
            (n.innerHTML =
              '\n    <div class="modal" id="modal">\n        <div class="modal-header">\n            <div id="addon-info-title"></div>\n        </div>\n        <div class="modal-content">\n        </div>\n        <div class="modal-footer">            \n        </div>\n    </div>\n    '),
            document.body.appendChild(n),
            n
          );
        };
        function s(e, n, t) {
          const r = document.createElement("button");
          return (
            r.classList.add("btn", e),
            (r.textContent = n),
            r.addEventListener("click", function () {
              (null != t && t(), i());
            }),
            r
          );
        }
        function a() {
          const e = document.getElementById("modal");
          ((e.style.display = "block"),
            setTimeout(() => {
              e.classList.add("show");
            }, 10));
        }
        function i() {
          const e = document.getElementById("modal");
          (e.classList.remove("show"),
            setTimeout(() => {
              ((e.style.display = "none"), clearInterval(t));
            }, 300));
        }
        ((n.displayMessage = function (e, t = 10) {
          O(location.href) &&
            (e.type == o.goSubscribe
              ? (console.log(e),
                console.log(e.mainAction),
                e.mainAction && "blank" == e.mainAction
                  ? (console.log(111),
                    n.openModalWithButton(
                      e.title,
                      e.text,
                      e.mainText,
                      () => {
                        window.open(e.mainUrl, "_blank");
                      },
                      e.subText,
                      null,
                    ))
                  : n.openModalWithButton(
                      e.title,
                      e.text,
                      e.mainText,
                      () => {
                        window.location.href = e.mainUrl;
                      },
                      e.subText,
                      null,
                    ))
              : e.type == o.pureNotice &&
                n.openModalWithTimer(e.title, e.text, t));
        }),
          (n.openModalWithTimer = function (n, o, s = 10) {
            if (O(location.href)) {
              r();
              const d = document.getElementById("addon-info-title"),
                l = document.querySelector(".modal-content"),
                c = document.querySelector(".modal-footer");
              (clearInterval(t),
                (l.innerHTML = o),
                (c.innerHTML = ""),
                (d.innerHTML = n));
              const m = document.createElement("div");
              ((m.id = "countdown"),
                (m.textContent = `close in ${s}s`),
                c.appendChild(m));
              let g = s;
              t = setInterval(() => {
                g <= 0
                  ? (clearInterval(t), i())
                  : ((m.textContent = `close in ${g}s`), g--);
              }, 1e3);
              const u = document.querySelector(".noticButtonP .noticA");
              (u &&
                (u.onclick = () =>
                  e(this, void 0, void 0, function* () {
                    let e = yield b("discordUrl");
                    (window.open(e + "", "_blank"),
                      yield v("isJumpDiscord", !0));
                  })),
                a());
            }
          }),
          (n.openModalWithButton = function (e, n, o, i, d, l) {
            if (O(location.href)) {
              r();
              const c = document.getElementById("addon-info-title"),
                m = document.querySelector(".modal-content"),
                g = document.querySelector(".modal-footer");
              if (
                (clearInterval(t),
                (m.innerHTML = n),
                (g.innerHTML = ""),
                (c.innerHTML = e),
                null != o)
              ) {
                const e = s("btn-wishlist", o, i);
                g.appendChild(e);
              }
              if (null != d) {
                const e = s("btn-no-thanks", d, l);
                g.appendChild(e);
              }
              const u = document.querySelector(".notice_openSiderpanle");
              (u &&
                (u.onclick = () => {
                  let e = p().runtime.connect({ name: "openSidepanels" });
                  (e.postMessage({}), e.disconnect());
                }),
                a());
            }
          }));
      })(A || (A = {}));
      var x = (function () {
        var e = "undefined" != typeof self ? self : this,
          n = {
            navigator: void 0 !== e.navigator ? e.navigator : {},
            infoMap: {
              engine: ["WebKit", "Trident", "Gecko", "Presto"],
              browser: [
                "Safari",
                "Chrome",
                "Edge",
                "IE",
                "Firefox",
                "Firefox Focus",
                "Chromium",
                "Opera",
                "Vivaldi",
                "Yandex",
                "Arora",
                "Lunascape",
                "QupZilla",
                "Coc Coc",
                "Kindle",
                "Iceweasel",
                "Konqueror",
                "Iceape",
                "SeaMonkey",
                "Epiphany",
                "360",
                "360SE",
                "360EE",
                "UC",
                "QQBrowser",
                "QQ",
                "Baidu",
                "Maxthon",
                "Sogou",
                "LBBROWSER",
                "2345Explorer",
                "TheWorld",
                "XiaoMi",
                "Quark",
                "Qiyu",
                "Wechat",
                ,
                "WechatWork",
                "Taobao",
                "Alipay",
                "Weibo",
                "Douban",
                "Suning",
                "iQiYi",
              ],
              os: [
                "Windows",
                "Linux",
                "Mac OS",
                "Android",
                "Ubuntu",
                "FreeBSD",
                "Debian",
                "iOS",
                "Windows Phone",
                "BlackBerry",
                "MeeGo",
                "Symbian",
                "Chrome OS",
                "WebOS",
              ],
              device: ["Mobile", "Tablet", "iPad"],
            },
          },
          t = {
            createUUID: function () {
              for (var e = [], n = "0123456789abcdef", t = 0; t < 36; t++)
                e[t] = n.substr(Math.floor(16 * Math.random()), 1);
              return (
                (e[14] = "4"),
                (e[19] = n.substr((3 & e[19]) | 8, 1)),
                (e[8] = e[13] = e[18] = e[23] = "-"),
                e.join("")
              );
            },
            getDate: function () {
              var e = new Date(),
                n = e.getFullYear(),
                t = e.getMonth() + 1,
                r = e.getDate(),
                o = e.getHours(),
                s = e.getMinutes(),
                a = e.getSeconds();
              return ""
                .concat(n.toString(), "/")
                .concat(t.toString(), "/")
                .concat(r.toString(), " ")
                .concat(o.toString(), ":")
                .concat(s.toString(), ":")
                .concat(a.toString());
            },
            getTimezoneOffset: function () {
              return new Date().getTimezoneOffset();
            },
            getTimezone: function () {
              return Intl.DateTimeFormat().resolvedOptions().timeZone;
            },
            getMatchMap: function (e) {
              return {
                Trident: e.indexOf("Trident") > -1 || e.indexOf("NET CLR") > -1,
                Presto: e.indexOf("Presto") > -1,
                WebKit: e.indexOf("AppleWebKit") > -1,
                Gecko: e.indexOf("Gecko/") > -1,
                Safari: e.indexOf("Safari") > -1,
                Chrome: e.indexOf("Chrome") > -1 || e.indexOf("CriOS") > -1,
                IE: e.indexOf("MSIE") > -1 || e.indexOf("Trident") > -1,
                Edge: e.indexOf("Edge") > -1,
                Firefox: e.indexOf("Firefox") > -1 || e.indexOf("FxiOS") > -1,
                "Firefox Focus": e.indexOf("Focus") > -1,
                Chromium: e.indexOf("Chromium") > -1,
                Opera: e.indexOf("Opera") > -1 || e.indexOf("OPR") > -1,
                Vivaldi: e.indexOf("Vivaldi") > -1,
                Yandex: e.indexOf("YaBrowser") > -1,
                Arora: e.indexOf("Arora") > -1,
                Lunascape: e.indexOf("Lunascape") > -1,
                QupZilla: e.indexOf("QupZilla") > -1,
                "Coc Coc": e.indexOf("coc_coc_browser") > -1,
                Kindle: e.indexOf("Kindle") > -1 || e.indexOf("Silk/") > -1,
                Iceweasel: e.indexOf("Iceweasel") > -1,
                Konqueror: e.indexOf("Konqueror") > -1,
                Iceape: e.indexOf("Iceape") > -1,
                SeaMonkey: e.indexOf("SeaMonkey") > -1,
                Epiphany: e.indexOf("Epiphany") > -1,
                360:
                  e.indexOf("QihooBrowser") > -1 || e.indexOf("QHBrowser") > -1,
                "360EE": e.indexOf("360EE") > -1,
                "360SE": e.indexOf("360SE") > -1,
                UC: e.indexOf("UC") > -1 || e.indexOf(" UBrowser") > -1,
                QQBrowser: e.indexOf("QQBrowser") > -1,
                QQ: e.indexOf("QQ/") > -1,
                Baidu: e.indexOf("Baidu") > -1 || e.indexOf("BIDUBrowser") > -1,
                Maxthon: e.indexOf("Maxthon") > -1,
                Sogou: e.indexOf("MetaSr") > -1 || e.indexOf("Sogou") > -1,
                LBBROWSER:
                  e.indexOf("LBBROWSER") > -1 || e.indexOf("LieBaoFast") > -1,
                "2345Explorer": e.indexOf("2345Explorer") > -1,
                TheWorld: e.indexOf("TheWorld") > -1,
                XiaoMi: e.indexOf("MiuiBrowser") > -1,
                Quark: e.indexOf("Quark") > -1,
                Qiyu: e.indexOf("Qiyu") > -1,
                Wechat: e.indexOf("MicroMessenger") > -1,
                WechatWork: e.indexOf("wxwork/") > -1,
                Taobao: e.indexOf("AliApp(TB") > -1,
                Alipay: e.indexOf("AliApp(AP") > -1,
                Weibo: e.indexOf("Weibo") > -1,
                Douban: e.indexOf("com.douban.frodo") > -1,
                Suning: e.indexOf("SNEBUY-APP") > -1,
                iQiYi: e.indexOf("IqiyiApp") > -1,
                DingTalk: e.indexOf("DingTalk") > -1,
                Vivo: e.indexOf("VivoBrowser") > -1,
                Huawei:
                  e.indexOf("HuaweiBrowser") > -1 ||
                  e.indexOf("HUAWEI/") > -1 ||
                  e.indexOf("HONOR") > -1 ||
                  e.indexOf("HBPC/") > -1,
                Windows: e.indexOf("Windows") > -1,
                Linux: e.indexOf("Linux") > -1 || e.indexOf("X11") > -1,
                "Mac OS": e.indexOf("Macintosh") > -1,
                Android: e.indexOf("Android") > -1 || e.indexOf("Adr") > -1,
                Ubuntu: e.indexOf("Ubuntu") > -1,
                FreeBSD: e.indexOf("FreeBSD") > -1,
                Debian: e.indexOf("Debian") > -1,
                "Windows Phone":
                  e.indexOf("IEMobile") > -1 || e.indexOf("Windows Phone") > -1,
                BlackBerry:
                  e.indexOf("BlackBerry") > -1 || e.indexOf("RIM") > -1,
                MeeGo: e.indexOf("MeeGo") > -1,
                Symbian: e.indexOf("Symbian") > -1,
                iOS: e.indexOf("like Mac OS X") > -1,
                "Chrome OS": e.indexOf("CrOS") > -1,
                WebOS: e.indexOf("hpwOS") > -1,
                Mobile:
                  e.indexOf("Mobi") > -1 ||
                  e.indexOf("iPh") > -1 ||
                  e.indexOf("480") > -1,
                Tablet: e.indexOf("Tablet") > -1 || e.indexOf("Nexus 7") > -1,
                iPad: e.indexOf("iPad") > -1,
              };
            },
            matchInfoMap: function (e) {
              var r = n.navigator.userAgent || {},
                o = t.getMatchMap(r);
              for (var s in n.infoMap)
                for (var a = 0; a < n.infoMap[s].length; a++) {
                  var i = n.infoMap[s][a];
                  o[i] && (e[s] = i);
                }
            },
            getOS: function () {
              return (t.matchInfoMap(this), this.os);
            },
            getOSVersion: function () {
              var e = this,
                t = n.navigator.userAgent || {};
              e.osVersion = "";
              var r = {
                Windows: function () {
                  var e = t.replace(/^.*Windows NT ([\d.]+);.*$/, "$1");
                  return (
                    {
                      10: "10 || 11",
                      6.3: "8.1",
                      6.2: "8",
                      6.1: "7",
                      "6.0": "Vista",
                      5.2: "XP 64-Bit",
                      5.1: "XP",
                      "5.0": "2000",
                      "4.0": "NT 4.0",
                      "3.5.1": "NT 3.5.1",
                      3.5: "NT 3.5",
                      3.1: "NT 3.1",
                    }[e] || e
                  );
                },
                Android: function () {
                  return t.replace(/^.*Android ([\d.]+);.*$/, "$1");
                },
                iOS: function () {
                  return t
                    .replace(/^.*OS ([\d_]+) like.*$/, "$1")
                    .replace(/_/g, ".");
                },
                Debian: function () {
                  return t.replace(/^.*Debian\/([\d.]+).*$/, "$1");
                },
                "Windows Phone": function () {
                  return t.replace(/^.*Windows Phone( OS)? ([\d.]+);.*$/, "$2");
                },
                "Mac OS": function () {
                  return t
                    .replace(/^.*Mac OS X ([\d_]+).*$/, "$1")
                    .replace(/_/g, ".");
                },
                WebOS: function () {
                  return t.replace(/^.*hpwOS\/([\d.]+);.*$/, "$1");
                },
              };
              return (
                r[e.os] &&
                  ((e.osVersion = r[e.os]()),
                  e.osVersion == t && (e.osVersion = "")),
                e.osVersion
              );
            },
            getDeviceType: function () {
              var e = this;
              return ((e.device = "PC"), t.matchInfoMap(e), e.device);
            },
            getNetwork: function () {
              return "";
            },
            getLanguage: function () {
              var e;
              return (
                (this.language =
                  ((e = (
                    n.navigator.browserLanguage || n.navigator.language
                  ).split("-"))[1] && (e[1] = e[1].toUpperCase()),
                  e.join("_"))),
                this.language
              );
            },
            getBrowserInfo: function () {
              var e = this;
              t.matchInfoMap(e);
              var r = n.navigator.userAgent || {},
                o = t.getMatchMap(r);
              if (
                (o.Baidu && o.Opera && (o.Baidu = !1),
                o.Mobile && (o.Mobile = !(r.indexOf("iPad") > -1)),
                o.IE || o.Edge)
              )
                switch (window.screenTop - window.screenY) {
                  case 71:
                  case 74:
                  case 99:
                  case 75:
                  case 74:
                  case 105:
                  default:
                    break;
                  case 102:
                    o["360EE"] = !0;
                    break;
                  case 104:
                    o["360SE"] = !0;
                }
              var s = {
                Safari: function () {
                  return r.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Chrome: function () {
                  return r
                    .replace(/^.*Chrome\/([\d.]+).*$/, "$1")
                    .replace(/^.*CriOS\/([\d.]+).*$/, "$1");
                },
                IE: function () {
                  return r
                    .replace(/^.*MSIE ([\d.]+).*$/, "$1")
                    .replace(/^.*rv:([\d.]+).*$/, "$1");
                },
                Edge: function () {
                  return r.replace(/^.*Edge\/([\d.]+).*$/, "$1");
                },
                Firefox: function () {
                  return r
                    .replace(/^.*Firefox\/([\d.]+).*$/, "$1")
                    .replace(/^.*FxiOS\/([\d.]+).*$/, "$1");
                },
                "Firefox Focus": function () {
                  return r.replace(/^.*Focus\/([\d.]+).*$/, "$1");
                },
                Chromium: function () {
                  return r.replace(/^.*Chromium\/([\d.]+).*$/, "$1");
                },
                Opera: function () {
                  return r
                    .replace(/^.*Opera\/([\d.]+).*$/, "$1")
                    .replace(/^.*OPR\/([\d.]+).*$/, "$1");
                },
                Vivaldi: function () {
                  return r.replace(/^.*Vivaldi\/([\d.]+).*$/, "$1");
                },
                Yandex: function () {
                  return r.replace(/^.*YaBrowser\/([\d.]+).*$/, "$1");
                },
                Arora: function () {
                  return r.replace(/^.*Arora\/([\d.]+).*$/, "$1");
                },
                Lunascape: function () {
                  return r.replace(/^.*Lunascape[\/\s]([\d.]+).*$/, "$1");
                },
                QupZilla: function () {
                  return r.replace(/^.*QupZilla[\/\s]([\d.]+).*$/, "$1");
                },
                "Coc Coc": function () {
                  return r.replace(/^.*coc_coc_browser\/([\d.]+).*$/, "$1");
                },
                Kindle: function () {
                  return r.replace(/^.*Version\/([\d.]+).*$/, "$1");
                },
                Iceweasel: function () {
                  return r.replace(/^.*Iceweasel\/([\d.]+).*$/, "$1");
                },
                Konqueror: function () {
                  return r.replace(/^.*Konqueror\/([\d.]+).*$/, "$1");
                },
                Iceape: function () {
                  return r.replace(/^.*Iceape\/([\d.]+).*$/, "$1");
                },
                SeaMonkey: function () {
                  return r.replace(/^.*SeaMonkey\/([\d.]+).*$/, "$1");
                },
                Epiphany: function () {
                  return r.replace(/^.*Epiphany\/([\d.]+).*$/, "$1");
                },
                Maxthon: function () {
                  return r.replace(/^.*Maxthon\/([\d.]+).*$/, "$1");
                },
              };
              return (
                (e.browserVersion = ""),
                s[e.browser] &&
                  ((e.browserVersion = s[e.browser]()),
                  e.browserVersion == r && (e.browserVersion = "")),
                "Chrome" == e.browser &&
                  r.match(/\S+Browser/) &&
                  ((e.browser = r.match(/\S+Browser/)[0]),
                  (e.version = r.replace(/^.*Browser\/([\d.]+).*$/, "$1"))),
                "Edge" == e.browser &&
                  (e.version > "75"
                    ? (e.engine = "Blink")
                    : (e.engine = "EdgeHTML")),
                (("Chrome" == e.browser && parseInt(e.browserVersion) > 27) ||
                  (o.Chrome &&
                    "WebKit" == e.engine &&
                    parseInt(s.Chrome()) > 27) ||
                  ("Opera" == e.browser && parseInt(e.version) > 12) ||
                  "Yandex" == e.browser) &&
                  (e.engine = "Blink"),
                e.browser +
                  "(version: " +
                  e.browserVersion +
                  "&nbsp;&nbsp;kernel: " +
                  e.engine +
                  ")"
              );
            },
            getGeoPostion: function () {
              return new Promise(function (e, n) {
                navigator && navigator.geolocation
                  ? navigator.geolocation.getCurrentPosition(
                      function (n) {
                        e(n);
                      },
                      function () {
                        e({ coords: { longitude: "fail", latitude: "fail" } });
                      },
                      { enableHighAccuracy: !1, timeout: 1e4 },
                    )
                  : n("fail");
              });
            },
            getPlatform: function () {
              return (
                (n.navigator.userAgentData &&
                  n.navigator.userAgentData.platform) ||
                n.navigator.platform
              );
            },
          },
          r = {
            DeviceInfoObj: function (e) {
              var r = {
                  deviceType: t.getDeviceType(),
                  os: t.getOS(),
                  osVersion: t.getOSVersion(),
                  platform: t.getPlatform(),
                  language: t.getLanguage(),
                  network: t.getNetwork(),
                  browserInfo: t.getBrowserInfo(),
                  userAgent: n.navigator.userAgent,
                  geoPosition: !0,
                  date: t.getDate(),
                  timezoneOffset: t.getTimezoneOffset(),
                  timezone: t.getTimezone(),
                  uuid: t.createUUID(),
                },
                o = {};
              if (e && e.info && 0 !== e.info.length) {
                var s = {},
                  a = function (n) {
                    e.info.forEach(function (e) {
                      e.toLowerCase() === n.toLowerCase() &&
                        (s[(e = n)] = r[e]);
                    });
                  };
                for (var i in r) a(i);
                o = s;
              } else o = r;
              return o;
            },
          };
        return {
          Info: function (e) {
            return r.DeviceInfoObj(e);
          },
        };
      })();
      function y() {
        return x.Info({
          info: [
            "deviceType",
            "OS",
            "OSVersion",
            "platform",
            "language",
            "netWork",
            "browserInfo",
            "screenHeight",
            "screenWidth",
            "userAgent",
            "appCodeName",
            "appName",
            "appVersion",
            "geoPosition",
            "date",
            "UUID",
            "timezoneOffset",
            "timezone",
          ],
        });
      }
      class w {
        static userReg(e, n, t, r, o, s) {
          const a = new FormData();
          let i = y();
          for (const e in i) a.append(e, i[e]);
          (a.append("userId", e),
            a.append("extId", n),
            a.append("version", t),
            a.append("action", r),
            a.append("detail", JSON.stringify(o)),
            console.log(
              "fetch url:https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
            ),
            fetch(
              "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-1",
              { method: "POST", body: a },
            ).then((e) => {
              s && s(e);
            }));
        }
        static logAction(n, t, r, o, s, a, i) {
          return e(this, void 0, void 0, function* () {
            console.log("====start log verion:" + r);
            const e = new FormData();
            let d = y();
            for (const n in d) e.append(n, d[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", r),
              e.append("action", o),
              e.append("detail", JSON.stringify(s)),
              a &&
                (e.append("url", a), e.append("domain", new URL(a).hostname)));
            let l = null;
            try {
              let n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/log-action-1",
                { method: "POST", body: e },
              );
              (i && i(n), (l = n.json()));
            } catch (e) {}
            return l;
          });
        }
        static userReg2(n, t, r, o, s, a, i, d) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let l = y();
            for (const n in l) e.append(n, l[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", r),
              e.append("action", o),
              e.append("email", s),
              e.append("password", a),
              e.append("emailCode", i),
              e.append("detail", JSON.stringify(d)));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/reg-2",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static userLogin(n, t, r, o, s, a, i) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let d = y();
            for (const n in d) e.append(n, d[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", r),
              e.append("action", o),
              e.append("email", s),
              e.append("password", a),
              e.append("detail", JSON.stringify(i)));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/login",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static sendEmailCode(n, t, r, o, s, a) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            let i = y();
            for (const n in i) e.append(n, i[n]);
            (e.append("userId", n),
              e.append("extId", t),
              e.append("version", r),
              e.append("action", o),
              e.append("email", s),
              e.append("detail", JSON.stringify(a)));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-reg/ude/getEmailCode",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyPre(n) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            e.append("pssh", n);
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyPre",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              const t = yield n.json();
              return 0 == t.code
                ? { success: !0, data: t.data }
                : { success: !1, error: t.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getSecretKeyAfter(n, t, r, o) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("session_id", n), e.append("licence", t));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getSecretKeyAfter",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              const t = yield n.json();
              return 0 == t.code
                ? (yield this.saveDrmKey(
                    "a5376339-a50f-f096-248c-0e0cdde4f9af",
                    r,
                    t.data.keys.trim(),
                    o,
                  ),
                  { success: !0, data: t.data })
                : { success: !1, error: t.msg };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static checkHasVideo(n, t) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", n), e.append("courseId", t));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/file/ude/checkHasVideo",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getExtDrmKey(n, t) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", n), e.append("courseId", t));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/getExtDrmKey",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static saveDrmKey(n, t, r, o) {
          return e(this, void 0, void 0, function* () {
            const e = new FormData();
            (e.append("extId", n),
              e.append("courseId", t),
              e.append("drmKey", r),
              e.append("userId", o));
            try {
              const n = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/drmKey/ude/saveDrmKey",
                { method: "POST", body: e },
              );
              if (!n.ok) {
                return { success: !1, error: yield n.json() };
              }
              return { success: !0, data: yield n.json() };
            } catch (e) {
              return { success: !1, error: e.message };
            }
          });
        }
        static getConfig() {
          return e(this, void 0, void 0, function* () {
            let e = "",
              n = !0;
            try {
              const t = new FormData();
              t.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af");
              const r = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/user-action/ude/getConfig",
                { method: "POST", body: t },
              );
              if (r.ok) {
                e = (yield r.json()).data;
              } else n = !1;
            } catch (e) {
              ((n = !1), console.log(e));
            }
            var t;
            return (
              !n && this.retryCount < this.retryMaxCount
                ? (this.retryCount++,
                  yield ((t = 1e3), new Promise((e) => setTimeout(e, t))),
                  this.getConfig())
                : (this.retryCount = 0),
              e
            );
          });
        }
        static getUserMemberInfo(n, t) {
          return e(this, void 0, void 0, function* () {
            // Return unlimited access for all users
            return {
              userId: n || "unlimited_user",
              memberName: "ProMember",
              maxDownloadCount: 999999,
              downloadedCount: 0,
              allDownloadedCount: 0,
              expiredTime: "2099-12-31",
              noPerDayMemberList: [],
            };
          });
        }
        static getDownloadCount(n) {
          return e(this, void 0, void 0, function* () {
            // Return 0 for local usage - unlimited downloads
            return 0;
          });
        }
        static updateNoPerDayDownloadCount(n) {
          return e(this, void 0, void 0, function* () {
            // Local update - no server needed
            console.log("Local download count update - unlimited downloads");
            return true;
          });
        }
        static downloadRecord(n, t) {
          return e(this, void 0, void 0, function* () {
            try {
              const e = new FormData();
              let r = y();
              for (const n in r) e.append(n, r[n]);
              (e.append("extId", "a5376339-a50f-f096-248c-0e0cdde4f9af"),
                e.append("userId", n),
                e.append("version", p().runtime.getManifest().version));
              let o = JSON.stringify({ perday: t });
              e.append("detail", o);
              const s = yield fetch(
                "https://www.bestaddons.store/ytbdserver/downloader/record/ude/downloadRecord",
                { method: "POST", body: e },
              );
              if (s.ok) {
                0 === (yield s.json()).code &&
                  console.log("downloadRecord success");
              }
            } catch (e) {
              console.log(e);
            }
          });
        }
      }
      ((w.retryCount = 0), (w.retryMaxCount = 3));
      function h() {
        return e(this, void 0, void 0, function* () {
          let e = yield b("UdemyDownloaderUserInfo");
          return null != e && null != e.userId && "" != e.userId ? e : null;
        });
      }
      function b(e) {
        return p()
          .storage.local.get(e)
          .then((n) => n[e]);
      }
      function v(e, n) {
        return p().storage.local.set({ [e]: n });
      }
      new (class {
        constructor() {
          this.locks = {};
        }
        acquire(n, t) {
          return e(this, void 0, void 0, function* () {
            const e = (this.locks[n] || Promise.resolve())
              .then(t)
              .catch(() => {});
            return ((this.locks[n] = e), e);
          });
        }
      })();
      function O(e) {
        return "udemy.com,udemybusiness,ssudemy.com,udemyfreecourses.org,discudemy.com,premiumm.click,freecourseudemy.com"
          .split(",")
          .some((n) => e.includes(n));
      }
      var C;
      let I = (C = class {
        constructor() {
          (C.initStyle(),
            // Removed setEmailAddress call
            C.getUserInfo(),
            // Removed jumpDiscard and copyEmail buttons
            // CIH99 - Removed button event listeners for non-existent elements
            console.log("CIH99 - Button event listeners removed"));
        }
        static initStyle() {
          const e = document.createElement("style");
          let n = "url(" + p().runtime.getURL("assets/imgs/Email.png") + ")",
            t =
              "url(" +
              p().runtime.getURL("assets/imgs/discard-green.png") +
              ")",
            r = "url(" + p().runtime.getURL("assets/imgs/optionlogo.png") + ")";
          ((e.innerHTML = `\n            :root {\n                --addon-email: ${n};\n                --addon-discard-green: ${t};\n                --addon-optionlogo: ${r};\n            }\n        `),
            document.head.appendChild(e));
        }
        // Removed setEmailAddress function
        static getUserInfo() {
          return e(this, void 0, void 0, function* () {
            const e = yield C.getUserMemberInfo();
            // CIH99 - Removed unused destructured variables
            // CIH99 - Only set userId if element exists
            const userIdElement = document.getElementById("userId");
            if (userIdElement) {
              userIdElement.textContent = e.userId;
            }
            // CIH99 - Simplified user info display, removed non-existent elements
            console.log("CIH99 - User info loaded:", e.userId);
            // CIH99 - Removed all UI manipulation for non-existent elements
          });
        }
        static calculateNoPerDayDownloadCount(n) {
          return e(this, void 0, void 0, function* () {
            let e = n.noPerDayMemberList,
              t = 0,
              r = 0,
              o = "";
            if (null != e && e.length > 0) {
              for (let n = 0; n < e.length; n++) {
                ((t += null == e[n].downloadCount ? 0 : e[n].downloadCount),
                  (r +=
                    null == e[n].downloadedCount ? 0 : e[n].downloadedCount));
              }
              o = e[0].memberName;
            }
            return {
              noPerDayMaxDownloadCount: t,
              hasDownloadCount: r,
              noPerDayMemberName: o,
            };
          });
        }
        static jumpPay() {
          return e(this, void 0, void 0, function* () {
            // Local activation - no external server needed
            console.log(
              "Local activation enabled - unlimited downloads available",
            );
            alert("You already have unlimited downloads enabled!");
          });
        }
        // Removed jumpDiscard and copyEmail functions
        static getUserMemberInfo() {
          return e(this, void 0, void 0, function* () {
            let e = yield h();
            const n = yield w.getUserMemberInfo(
              e.userId,
              p().runtime.getManifest().version,
            );
            return (
              n &&
                ((e.noPerDayMemberList = n.noPerDayMemberList),
                (e.memberName = n.memberName),
                (e.maxDownloadCount = n.maxDownloadCount),
                (e.downloadedCount =
                  null == n.downloadedCount ? 0 : n.downloadedCount),
                (e.allDownloadedCount =
                  null == n.allDownloadedCount ? 0 : n.allDownloadedCount),
                (e.expiredTime = n.expiredTime),
                (e.canBusiness = n.canBusiness),
                yield v("UdemyDownloaderUserInfo", e),
                yield v("downloadCount", e.downloadedCount)),
              e
            );
          });
        }
      });
      I = C = (function (e, n, t, r) {
        var o,
          s = arguments.length,
          a =
            s < 3
              ? n
              : null === r
                ? (r = Object.getOwnPropertyDescriptor(n, t))
                : r;
        if ("object" == typeof Reflect && "function" == typeof Reflect.decorate)
          a = Reflect.decorate(e, n, t, r);
        else
          for (var i = e.length - 1; i >= 0; i--)
            (o = e[i]) &&
              (a = (s < 3 ? o(a) : s > 3 ? o(n, t, a) : o(n, t)) || a);
        return (s > 3 && a && Object.defineProperty(n, t, a), a);
      })(
        [
          function (e) {
            return new Proxy(e, {
              construct: (e, n, t) =>
                e.prototype !== t.prototype
                  ? Reflect.construct(e, n, t)
                  : (e.SINGLETON_INSTANCE ||
                      (e.SINGLETON_INSTANCE = Reflect.construct(e, n, t)),
                    e.SINGLETON_INSTANCE),
            });
          },
        ],
        I,
      );
      new I();
    })());
})();
