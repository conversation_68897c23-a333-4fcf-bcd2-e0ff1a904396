import sys
import subprocess
import os
from PyQt6.QtWidgets import (QApplication, QWidget, QLabel, QLineEdit, QPushButton, QVBoxLayout, QProgressBar, QMessageBox, QTextEdit, QFrame, QGridLayout)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QKeySequence, QShortcut

class DownloaderThread(QThread):
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool)
    stage_signal = pyqtSignal(str)  # إشارة لمرحلة العملية


    def __init__(self, exe_path, mpd_url, key, filename):
        super().__init__()
        self.exe_path = exe_path
        self.mpd_url = mpd_url
        self.key = key
        self.filename = filename


    def run(self):
        try:
            # المرحلة الوحيدة: عرض خيارات الجودة في الكونسول والتنزيل
            self.stage_signal.emit("🔍 جاري عرض خيارات الجودة في الكونسول...")
            if not self.show_streams_in_console():
                self.finished_signal.emit(False)
                return

            self.stage_signal.emit("✅ تم الانتهاء بنجاح!")
            self.finished_signal.emit(True)

        except Exception as e:
            self.progress_signal.emit(f"خطأ: {str(e)}")
            self.finished_signal.emit(False)



    def show_streams_in_console(self):
        """عرض خيارات الجودة في كونسول منفصل للاختيار اليدوي"""
        try:
            self.progress_signal.emit("🔍 جاري فحص الجودات المتاحة...")
            self.progress_signal.emit("=" * 60)

            # تشغيل N_m3u8DL-RE بدون اختيار تلقائي للسماح بالاختيار اليدوي
            command = [
                self.exe_path,
                self.mpd_url,
                "--key", self.key,
                "--save-name", self.filename
                # بدون --select-video أو --select-audio للسماح بالاختيار اليدوي
            ]

            self.progress_signal.emit(f"🚀 تشغيل الأمر:")
            command_str = ' '.join([f'"{arg}"' if ' ' in arg else arg for arg in command])
            self.progress_signal.emit(f"   {command_str}")
            self.progress_signal.emit("")
            self.progress_signal.emit("🖥️ ستفتح نافذة كونسول منفصلة")
            self.progress_signal.emit("🎯 اختر الجودة المطلوبة من الكونسول")
            self.progress_signal.emit("⌨️ استخدم مفاتيح الأسهم و Space و Enter للاختيار")
            self.progress_signal.emit("� بعد الاختيار سيبدأ التنزيل تلقائياً")
            self.progress_signal.emit("")
            self.progress_signal.emit("⏳ جاري تشغيل N_m3u8DL-RE...")

            # تشغيل الأمر مع إنشاء كونسول منفصل
            process = subprocess.Popen(
                command,
                creationflags=subprocess.CREATE_NEW_CONSOLE  # إنشاء نافذة كونسول منفصلة
            )

            self.progress_signal.emit("")
            self.progress_signal.emit("✅ تم فتح نافذة الكونسول")
            self.progress_signal.emit("👀 راجع نافذة الكونسول المنفصلة لاختيار الجودة")
            self.progress_signal.emit("⏳ انتظار انتهاء العملية...")

            # انتظار انتهاء العملية
            return_code = process.wait()

            if return_code == 0:
                self.progress_signal.emit("")
                self.progress_signal.emit("✅ تم التنزيل بنجاح!")
                self.progress_signal.emit("📁 تحقق من المجلد للملفات المنزلة")
                self.progress_signal.emit("=" * 60)
                return True
            else:
                self.progress_signal.emit("")
                self.progress_signal.emit(f"❌ فشل التنزيل - رمز الخطأ: {return_code}")
                self.progress_signal.emit("💡 تحقق من صحة الرابط والمفتاح")
                self.progress_signal.emit("=" * 60)
                return False

        except Exception as e:
            self.progress_signal.emit(f"❌ خطأ في التنزيل: {str(e)}")
            return False







class CIH99Downloader(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_modern_style()

    def setup_ui(self):
        self.setWindowTitle("CIH99 PRO DRM Downloader")
        self.setGeometry(400, 200, 800, 700)
        self.setMinimumSize(700, 600)  # حد أدنى أكبر للحجم
        # إزالة setFixedSize للسماح بالتكبير والتصغير

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(5)  # تقليل المسافات
        main_layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش

        # العنوان المدمج
        header_text = "🎬 CIH99 PRO DRM Downloader | 🎮 اختيار يدوي للجودة | 📺 قائمة تفاعلية | ⌨️ F11: شاشة كاملة"
        title_label = QLabel(header_text)
        title_label.setObjectName("compactTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # إطار الإدخال المدمج
        input_frame = QFrame()
        input_frame.setObjectName("compactInputFrame")
        input_layout = QGridLayout(input_frame)  # استخدام Grid بدلاً من VBox
        input_layout.setSpacing(5)
        input_layout.setContentsMargins(15, 10, 15, 10)

        # الصف الأول: MPD
        mpd_label = QLabel("🔗 MPD:")
        mpd_label.setObjectName("compactLabel")
        self.mpd_input = QLineEdit()
        self.mpd_input.setObjectName("compactInput")
        self.mpd_input.setPlaceholderText("رابط MPD...")
        input_layout.addWidget(mpd_label, 0, 0)
        input_layout.addWidget(self.mpd_input, 0, 1)

        # الصف الثاني: المفتاح
        key_label = QLabel("🔑 المفتاح:")
        key_label.setObjectName("compactLabel")
        self.key_input = QLineEdit()
        self.key_input.setObjectName("compactInput")
        self.key_input.setPlaceholderText("KID:KEY...")
        input_layout.addWidget(key_label, 1, 0)
        input_layout.addWidget(self.key_input, 1, 1)

        # الصف الثالث: اسم الملف + زر التنزيل
        filename_label = QLabel("📁 الاسم:")
        filename_label.setObjectName("compactLabel")
        self.filename_input = QLineEdit()
        self.filename_input.setObjectName("compactInput")
        self.filename_input.setPlaceholderText("اسم الملف...")

        # زر التنزيل في نفس الصف
        self.download_button = QPushButton("🚀 تنزيل")
        self.download_button.setObjectName("compactDownloadButton")
        self.download_button.clicked.connect(self.start_download)

        input_layout.addWidget(filename_label, 2, 0)
        input_layout.addWidget(self.filename_input, 2, 1)
        input_layout.addWidget(self.download_button, 2, 2)

        # تعيين نسب الأعمدة
        input_layout.setColumnStretch(0, 0)  # العمود الأول (التسميات) - ثابت
        input_layout.setColumnStretch(1, 1)  # العمود الثاني (الحقول) - قابل للتمدد
        input_layout.setColumnStretch(2, 0)  # العمود الثالث (الزر) - ثابت

        main_layout.addWidget(input_frame)

        # إطار التقدم
        progress_frame = QFrame()
        progress_frame.setObjectName("progressFrame")
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setSpacing(8)
        progress_layout.setContentsMargins(20, 15, 20, 15)

        # تسمية المرحلة
        self.stage_label = QLabel("")
        self.stage_label.setObjectName("stageLabel")
        self.stage_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.stage_label.hide()
        progress_layout.addWidget(self.stage_label)

        # شريط التقدم
        self.progress = QProgressBar()
        self.progress.setObjectName("modernProgress")
        self.progress.setMaximum(0)
        self.progress.setValue(0)
        self.progress.hide()
        progress_layout.addWidget(self.progress)

        # منطقة عرض التقدم التفصيلي (الكونسول)
        self.progress_text = QTextEdit()
        self.progress_text.setObjectName("progressText")
        self.progress_text.setMinimumHeight(350)  # زيادة الحد الأدنى
        self.progress_text.setPlaceholderText("🖥️ منطقة الكونسول - ستظهر هنا تفاصيل العملية...")

        # تطبيق خط Calibri Bold مباشرة على الكونسول
        console_font = QFont("Calibri", 14)
        console_font.setBold(True)
        self.progress_text.setFont(console_font)

        self.progress_text.show()  # عملإظهار دائمالا
        progress_layout.addWidget(self.progress_text)

        main_layout.addWidget(progress_frame)

        self.setLayout(main_layout)

        # إضافة اختصار للشاشة الكاملة
        self.setup_fullscreen_shortcut()

        # رسالة ترحيب في الكونسول
        self.show_welcome_message()

    def show_welcome_message(self):
        """عرض رسالة ترحيب في الكونسول"""
        welcome_text = [
            "🎬 CIH99 PRO DRM Downloader v2.0",
            "=" * 50,
            "✨ مرحباً بك في أداة تنزيل الفيديوهات المحمية",
            "",
            "🎯 المميزات الرئيسية:",
            "  • 🎮 اختيار الجودة يدوياً من قائمة تفاعلية",
            "  • 📺 عرض جميع الجودات المتاحة (فيديو + صوت)",
            "  • ⚡ تنزيل مباشر بالجودة المختارة",
            "  • 🖥️ كونسول منفصل للاختيار المريح",
            "  • 🔧 تنظيف تلقائي للروابط من فلاتر الجودة",
            "",
            "📋 خطوات الاستخدام:",
            "  1️⃣ أدخل رابط MPD",
            "  2️⃣ أدخل مفتاح التشفير (KID:KEY)",
            "  3️⃣ أدخل اسم الملف المطلوب",
            "  4️⃣ اضغط زر التنزيل",
            "",
            "🎮 كيفية الاختيار في الكونسول:",
            "  • ستظهر قائمة بجميع الجودات المتاحة",
            "  • استخدم مفاتيح الأسهم ↑↓ للتنقل",
            "  • اضغط Space لتحديد/إلغاء تحديد الجودة",
            "  • اضغط Enter لبدء التنزيل بالجودة المختارة",
            "  • يمكنك اختيار فيديو + صوت منفصلين",
            "",
            "⌨️ اختصارات التطبيق:",
            "  • F11: تبديل الشاشة الكاملة",
            "  • Escape: الخروج من الشاشة الكاملة",
            "",
            "🔧 متطلبات:",
            f"  • N_m3u8DL-RE.exe: {'✅ موجود' if os.path.exists('N_m3u8DL-RE.exe') else '❌ غير موجود'}",
            "",
            "� ميزة التنظيف التلقائي:",
            "  • يكتشف فلاتر aws.manifestfilter تلقائياً",
            "  • ينظف الرابط لإظهار جميع الجودات",
            "  • يسأل موافقتك قبل استخدام الرابط المنظف",
            "",
            "🔧 ميزة التنظيف التلقائي:",
            "  • يكتشف فلاتر aws.manifestfilter تلقائياً",
            "  • ينظف الرابط لإظهار جميع الجودات",
            "  • يسأل موافقتك قبل استخدام الرابط المنظف",
            "",
            "�💡 نصيحة: ستحصل على قائمة تفاعلية مثل الصورة أعلاه!",
            "📝 جاهز للاستخدام! أدخل البيانات المطلوبة أعلاه.",
            "=" * 50
        ]

        for line in welcome_text:
            self.progress_text.append(line)

    def setup_fullscreen_shortcut(self):
        """إعداد اختصار الشاشة الكاملة"""
        # اختصار F11 للشاشة الكاملة
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_fullscreen)

        # اختصار Escape للخروج من الشاشة الكاملة
        self.escape_shortcut = QShortcut(QKeySequence("Escape"), self)
        self.escape_shortcut.activated.connect(self.exit_fullscreen)

        # متغير لتتبع حالة الشاشة الكاملة
        self.is_fullscreen = False
        self.normal_geometry = None

    def toggle_fullscreen(self):
        """تبديل الشاشة الكاملة"""
        if self.is_fullscreen:
            self.exit_fullscreen()
        else:
            self.enter_fullscreen()

    def enter_fullscreen(self):
        """دخول الشاشة الكاملة"""
        if not self.is_fullscreen:
            # حفظ الموضع والحجم الحالي
            self.normal_geometry = self.geometry()
            # تفعيل الشاشة الكاملة
            self.showFullScreen()
            self.is_fullscreen = True

    def exit_fullscreen(self):
        """الخروج من الشاشة الكاملة"""
        if self.is_fullscreen:
            # العودة للحجم العادي
            self.showNormal()
            if self.normal_geometry:
                self.setGeometry(self.normal_geometry)
            self.is_fullscreen = False

    def apply_modern_style(self):
        """تطبيق التصميم الحديث Dark Mode بنمط Windows 11"""
        style = """
        QWidget {
            background-color: #1e1e1e;
            font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
            font-weight: bold;
            color: #ffffff;
        }

        * {
            font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
            font-weight: bold;
        }

        #compactTitle {
            font-size: 14px;
            font-weight: bold;
            color: #60cdff;
            margin: 5px 0;
            padding: 5px;
            background-color: #2a2a2a;
            border: 1px solid #404040;
            border-radius: 4px;
        }

        #compactInputFrame {
            background-color: #2d2d30;
            border: 1px solid #3f3f46;
            border-radius: 6px;
            margin: 5px 0;
        }

        #compactLabel {
            font-size: 11px;
            font-weight: 600;
            color: #ffffff;
            min-width: 70px;
            max-width: 70px;
        }

        #compactInput {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 11px;
            color: #ffffff;
            height: 24px;
        }

        #compactInput:focus {
            border-color: #60cdff;
            outline: none;
        }

        #compactInput:hover {
            border-color: #7a7a7a;
        }

        #compactDownloadButton {
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 11px;
            font-weight: 600;
            height: 28px;
            min-width: 80px;
            max-width: 80px;
        }

        #compactDownloadButton:hover {
            background-color: #106ebe;
        }

        #compactDownloadButton:pressed {
            background-color: #005a9e;
        }

        #compactDownloadButton:disabled {
            background-color: #4a4a4a;
            color: #8a8a8a;
        }



        #progressFrame {
            background-color: #2d2d30;
            border: 1px solid #3f3f46;
            border-radius: 8px;
            margin: 10px 0;
        }

        #stageLabel {
            font-size: 14px;
            font-weight: 600;
            color: #60cdff;
            margin-bottom: 8px;
        }

        #modernProgress {
            border: none;
            border-radius: 4px;
            background-color: #3c3c3c;
            height: 6px;
        }

        #modernProgress::chunk {
            background-color: #60cdff;
            border-radius: 4px;
        }

        #progressText {
            background-color: #252526;
            border: 1px solid #3f3f46;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Calibri', 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
            color: #ffffff;
            line-height: 1.6;
            selection-background-color: #60cdff;
            selection-color: #000000;
        }

        QMessageBox {
            background-color: #2d2d30;
            color: #ffffff;
            border: 1px solid #3f3f46;
            font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
            font-weight: bold;
        }

        QMessageBox QLabel {
            color: #ffffff;
            background-color: transparent;
            font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
            font-weight: bold;
        }

        QMessageBox QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
            font-weight: bold;
            min-width: 80px;
            margin: 5px;
        }

        QMessageBox QPushButton:hover {
            background-color: #106ebe;
        }

        QMessageBox QPushButton:pressed {
            background-color: #005a9e;
        }
        """
        self.setStyleSheet(style)

    def clean_mpd_url(self, url):
        """تنظيف رابط MPD من الفلاتر التي تحد من الجودات المتاحة"""
        # إزالة aws.manifestfilter وأي معاملات أخرى قد تحد من الجودات
        if '?' in url:
            base_url = url.split('?')[0]
            # التحقق من وجود معاملات مفيدة نريد الاحتفاظ بها
            query_params = url.split('?')[1] if '?' in url else ''

            # قائمة المعاملات التي نريد إزالتها لأنها تحد من الجودات
            filters_to_remove = [
                'aws.manifestfilter',
                'manifestfilter',
                'filter',
                'quality_filter',
                'resolution_filter'
            ]

            # فصل المعاملات
            if query_params:
                params = query_params.split('&')
                clean_params = []

                for param in params:
                    param_name = param.split('=')[0].lower()
                    # الاحتفاظ فقط بالمعاملات التي لا تحد من الجودات
                    if not any(filter_name in param_name for filter_name in filters_to_remove):
                        clean_params.append(param)

                # إعادة بناء الرابط
                if clean_params:
                    return base_url + '?' + '&'.join(clean_params)
                else:
                    return base_url
            else:
                return base_url

        return url

    def start_download(self):
        mpd_url = self.mpd_input.text().strip()
        key = self.key_input.text().strip()
        filename = self.filename_input.text().strip()

        # التحقق من الحقول المطلوبة
        if not mpd_url or not key or not filename:
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Icon.Warning)
            msg.setWindowTitle("⚠️ خطأ في الإدخال")
            msg.setText("يرجى ملء جميع الحقول المطلوبة")
            msg.setDetailedText("تأكد من إدخال:\n• رابط MPD\n• مفتاح التشفير\n• اسم الملف")
            msg.exec()
            return

        # تنظيف رابط MPD من الفلاتر
        original_url = mpd_url
        cleaned_url = self.clean_mpd_url(mpd_url)

        if original_url != cleaned_url:
            # إظهار رسالة للمستخدم عن التنظيف
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setWindowTitle("🔧 تنظيف الرابط")
            msg.setText("تم اكتشاف فلاتر تحد من الجودات المتاحة")
            msg.setDetailedText(f"الرابط الأصلي:\n{original_url[:100]}...\n\nالرابط المنظف:\n{cleaned_url[:100]}...\n\n💡 هذا سيعطيك جميع الجودات المتاحة!")
            msg.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
            msg.setDefaultButton(QMessageBox.StandardButton.Ok)

            if msg.exec() == QMessageBox.StandardButton.Ok:
                mpd_url = cleaned_url
                # تحديث حقل الإدخال بالرابط المنظف
                self.mpd_input.setText(mpd_url)
            else:
                return

        # التحقق من وجود N_m3u8DL-RE.exe
        exe_path = os.path.join(os.getcwd(), "N_m3u8DL-RE.exe")
        if not os.path.exists(exe_path):
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.setWindowTitle("❌ ملف مفقود")
            msg.setText("لم يتم العثور على N_m3u8DL-RE.exe")
            msg.setDetailedText(f"المسار المطلوب:\n{exe_path}")
            msg.exec()
            return

        # إظهار عناصر التقدم
        self.progress.show()
        self.stage_label.show()
        self.progress_text.clear()
        self.progress_text.append("🚀 بدء عملية التنزيل...")
        self.progress_text.append("=" * 50)
        self.download_button.setEnabled(False)

        # بدء العملية
        self.thread = DownloaderThread(exe_path, mpd_url, key, filename)
        self.thread.progress_signal.connect(self.update_progress)
        self.thread.stage_signal.connect(self.update_stage)
        self.thread.finished_signal.connect(self.download_finished)
        self.thread.start()

    def update_progress(self, text):
        """تحديث نص التقدم التفصيلي"""
        self.progress_text.append(text)
        # التمرير إلى الأسفل تلقائياً
        scrollbar = self.progress_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def update_stage(self, stage_text):
        """تحديث تسمية المرحلة الحالية"""
        self.stage_label.setText(stage_text)



    def download_finished(self, success):
        """إنهاء العملية"""
        self.progress.hide()
        self.stage_label.hide()
        self.download_button.setEnabled(True)

        if success:
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setWindowTitle("🎉 تم بنجاح")
            msg.setText("تم تنزيل الفيديو بنجاح!")
            msg.setDetailedText("✅ تم التنزيل\n✅ تم فك التشفير\n📁 تحقق من المجلد للملفات")
            msg.exec()
        else:
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.setWindowTitle("❌ فشلت العملية")
            msg.setText("فشل في تنزيل أو تحويل الفيديو")
            msg.setDetailedText("تحقق من:\n• صحة رابط MPD\n• صحة مفتاح التشفير\n• اتصال الإنترنت")
            msg.exec()

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # تطبيق خط Calibri Bold على كامل التطبيق
    font = QFont("Calibri", 10)
    font.setBold(True)  # جعل الخط عريض
    app.setFont(font)

    # تطبيق نمط حديث
    app.setStyle("Fusion")

    # إنشاء النافذة
    window = CIH99Downloader()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec())
