(()=>{"use strict";const e="https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd/ffmpeg-core.js";var t;!function(e){e.LOAD="LOAD",e.EXEC="EXEC",e.WRITE_FILE="WRITE_FILE",e.READ_FILE="READ_FILE",e.DELETE_FILE="DELETE_FILE",e.RENAME="RENAME",e.CREATE_DIR="CREATE_DIR",e.LIST_DIR="LIST_DIR",e.DELETE_DIR="DELETE_DIR",e.ERROR="ERROR",e.DOWNLOAD="DOWNLOAD",e.PROGRESS="PROGRESS",e.LOG="LOG",e.MOUNT="MOUNT",e.UNMOUNT="UNMOUNT"}(t||(t={}));const a=new Error("unknown message type"),r=new Error("ffmpeg is not loaded, call `await ffmpeg.load()` first"),s=(new Error("called FFmpeg.terminate()"),new Error("failed to import ffmpeg-core.js"));let o;self.onmessage=async({data:{id:E,type:n,data:c}})=>{const i=[];let R;try{if(n!==t.LOAD&&!o)throw r;switch(n){case t.LOAD:R=await(async({coreURL:a,wasmURL:r,workerURL:E})=>{const n=!o;try{a||(a=e),importScripts(a)}catch{if(a||(a=e.replace("/umd/","/esm/")),self.createFFmpegCore=(await import(a)).default,!self.createFFmpegCore)throw s}const c=a,i=r||a.replace(/.js$/g,".wasm"),R=E||a.replace(/.js$/g,".worker.js");return o=await self.createFFmpegCore({mainScriptUrlOrBlob:`${c}#${btoa(JSON.stringify({wasmURL:i,workerURL:R}))}`}),o.setLogger((e=>self.postMessage({type:t.LOG,data:e}))),o.setProgress((e=>self.postMessage({type:t.PROGRESS,data:e}))),n})(c);break;case t.EXEC:R=(({args:e,timeout:t=-1})=>{o.setTimeout(t),o.exec(...e);const a=o.ret;return o.reset(),a})(c);break;case t.WRITE_FILE:R=(({path:e,data:t})=>(o.FS.writeFile(e,t),!0))(c);break;case t.READ_FILE:R=(({path:e,encoding:t})=>o.FS.readFile(e,{encoding:t}))(c);break;case t.DELETE_FILE:R=(({path:e})=>(o.FS.unlink(e),!0))(c);break;case t.RENAME:R=(({oldPath:e,newPath:t})=>(o.FS.rename(e,t),!0))(c);break;case t.CREATE_DIR:R=(({path:e})=>(o.FS.mkdir(e),!0))(c);break;case t.LIST_DIR:R=(({path:e})=>{const t=o.FS.readdir(e),a=[];for(const r of t){const t=o.FS.stat(`${e}/${r}`),s=o.FS.isDir(t.mode);a.push({name:r,isDir:s})}return a})(c);break;case t.DELETE_DIR:R=(({path:e})=>(o.FS.rmdir(e),!0))(c);break;case t.MOUNT:R=(({fsType:e,options:t,mountPoint:a})=>{const r=e,s=o.FS.filesystems[r];return!!s&&(o.FS.mount(s,t,a),!0)})(c);break;case t.UNMOUNT:R=(({mountPoint:e})=>(o.FS.unmount(e),!0))(c);break;default:throw a}}catch(e){return void self.postMessage({id:E,type:t.ERROR,data:e.toString()})}R instanceof Uint8Array&&i.push(R.buffer),self.postMessage({id:E,type:n,data:R},i)}})();