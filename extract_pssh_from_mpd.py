#!/usr/bin/env python3
"""
استخراج PSSH من ملف MPD
"""

import requests
import re
import base64
from urllib.parse import unquote

def extract_pssh_from_mpd(mpd_url):
    """استخراج PSSH من ملف MPD"""
    print("🔍 جاري تحليل ملف MPD...")
    
    try:
        # تحميل ملف MPD
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(mpd_url, headers=headers)
        if response.status_code != 200:
            print(f"❌ خطأ في تحميل MPD: {response.status_code}")
            return None
        
        mpd_content = response.text
        print(f"✅ تم تحميل MPD ({len(mpd_content)} حرف)")
        
        # البحث عن ContentProtection مع PSSH
        pssh_pattern = r'<ContentProtection[^>]*schemeIdUri="urn:uuid:([^"]+)"[^>]*>([^<]*)</ContentProtection>'
        matches = re.findall(pssh_pattern, mpd_content, re.IGNORECASE)
        
        pssh_data = []
        
        for match in matches:
            scheme_id = match[0].replace('-', '').lower()
            content = match[1].strip()
            
            # التحقق من أنظمة DRM المعروفة
            drm_systems = {
                "edef8ba979d64acea3c827dcd51d21ed": "Widevine",
                "9a04f0799840428eaba2e65be0885f95": "PlayReady"
            }
            
            if scheme_id in drm_systems:
                print(f"🔐 تم العثور على {drm_systems[scheme_id]}: {scheme_id}")
                if content:
                    try:
                        # محاولة فك Base64
                        decoded = base64.b64decode(content)
                        pssh_data.append({
                            'system': drm_systems[scheme_id],
                            'system_id': scheme_id,
                            'pssh': content,
                            'decoded_size': len(decoded)
                        })
                        print(f"   📦 PSSH: {content[:50]}...")
                    except:
                        print(f"   ⚠️ فشل في فك Base64")
        
        # البحث عن PSSH في cenc:pssh
        cenc_pattern = r'<cenc:pssh[^>]*>([^<]+)</cenc:pssh>'
        cenc_matches = re.findall(cenc_pattern, mpd_content, re.IGNORECASE)
        
        for pssh in cenc_matches:
            pssh = pssh.strip()
            if pssh:
                try:
                    decoded = base64.b64decode(pssh)
                    pssh_data.append({
                        'system': 'CENC',
                        'system_id': 'cenc',
                        'pssh': pssh,
                        'decoded_size': len(decoded)
                    })
                    print(f"🔐 تم العثور على CENC PSSH: {pssh[:50]}...")
                except:
                    print(f"⚠️ فشل في فك CENC PSSH")
        
        return pssh_data
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return None

def extract_key_ids_from_mpd(mpd_url):
    """استخراج Key IDs من ملف MPD"""
    print("\n🔑 البحث عن Key IDs...")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(mpd_url, headers=headers)
        mpd_content = response.text
        
        # البحث عن default_KID
        kid_pattern = r'default_KID="([^"]+)"'
        kids = re.findall(kid_pattern, mpd_content, re.IGNORECASE)
        
        key_ids = []
        for kid in kids:
            # تحويل من UUID إلى Base64
            kid_clean = kid.replace('-', '')
            try:
                kid_bytes = bytes.fromhex(kid_clean)
                kid_b64 = base64.b64encode(kid_bytes).decode()
                key_ids.append({
                    'uuid': kid,
                    'hex': kid_clean,
                    'base64': kid_b64
                })
                print(f"🔑 Key ID: {kid} -> {kid_b64}")
            except:
                print(f"⚠️ فشل في تحويل Key ID: {kid}")
        
        return key_ids
        
    except Exception as e:
        print(f"❌ خطأ في استخراج Key IDs: {str(e)}")
        return []

def main():
    print("🎯 أداة استخراج PSSH و Key IDs من MPD")
    print("=" * 50)
    
    # الرابط الذي قدمته
    mpd_url = "https://www.udemy.com/assets/39324330/encrypted-files/out/v1/0faea95abb42418085bfff7face9f7c6/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.3WCtMlH5MRHpXgBi49KRG5-YYfpLfuFCqdiSUUIwKf0&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%22394050b5-9eed-49d3-b858-73903e13ac79%22%2Csu"
    
    # استخراج PSSH
    pssh_data = extract_pssh_from_mpd(mpd_url)
    
    if pssh_data:
        print(f"\n✅ تم العثور على {len(pssh_data)} PSSH:")
        for i, data in enumerate(pssh_data, 1):
            print(f"\n📦 PSSH {i} ({data['system']}):")
            print(f"   System ID: {data['system_id']}")
            print(f"   PSSH: {data['pssh']}")
            print(f"   الحجم: {data['decoded_size']} بايت")
    
    # استخراج Key IDs
    key_ids = extract_key_ids_from_mpd(mpd_url)
    
    if key_ids:
        print(f"\n🔑 تم العثور على {len(key_ids)} Key ID:")
        for kid in key_ids:
            print(f"   UUID: {kid['uuid']}")
            print(f"   Hex: {kid['hex']}")
            print(f"   Base64: {kid['base64']}")
    
    # إرشادات الاستخدام
    print("\n" + "=" * 50)
    print("💡 الخطوات التالية:")
    print("1. استخدم PSSH مع أداة فك التشفير")
    print("2. احصل على مفتاح فك التشفير من License Server")
    print("3. استخدم الأمر الصحيح:")
    
    if key_ids:
        example_kid = key_ids[0]['hex'].lower()
        print(f'   N_m3u8DL-RE.exe "MPD_URL" --key "{example_kid}:YOUR_ACTUAL_KEY" --save-name "wwe"')
    else:
        print('   N_m3u8DL-RE.exe "MPD_URL" --key "KID:KEY" --save-name "wwe"')
    
    print("\n⚠️ ملاحظة: تحتاج للحصول على مفتاح فك التشفير الفعلي من License Server")

if __name__ == "__main__":
    main()
