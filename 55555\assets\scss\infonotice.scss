.modal {
  width: auto;
  max-width: 30%;
  max-width: 30%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  transform: scale(0.7);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  z-index: 9999999;
}

.modal.show {
  transform: scale(1);
  opacity: 1;
  display: block;
}

.modal-header {
  background: #fd2d63;;
  padding: 1rem;
  border-radius: 12px 12px 0 0;
  text-align: center;
  font-weight: bold;
  color:white;
  font-size: 1.6rem;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #262626;
}

.modal-content {
  padding: 1rem;
  text-align: center;
  color: #333;
}

.modal-footer {
  display: flex;
  justify-content: center;
  padding: 0.5rem;
  border-top: 1px solid #e0e0e0;
  color: #bebbb7;
}

.btn {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-left: 0.5rem;
  font-size: 1.1rem;
  outline: none;
  transition: background-color 0.3s, transform 0.1s;
}

.btn-wishlist {
  background-color: #fd2d63;
  color: white;
}

.btn-no-thanks {
  background-color: #a9a6a6;
  color: white;
}

.btn-wishlist:hover {
  filter: brightness(90%);
  transform: scale(1.05);
  background-color: #d92555;
}

.btn-no-thanks:hover {
  filter: brightness(90%);
}
