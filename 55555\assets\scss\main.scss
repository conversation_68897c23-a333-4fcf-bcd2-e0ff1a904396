/*!
 * Copyright (c) 2022. HuiiBuh
 * This file (main.scss) is part of InstagramDownloader which is not released
 * under any licence.
 * Any usage of this code outside this project is not allowed.
 */

.post-save-btn {
  background-image: var(--addon-save-red);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.post-saving-btn {
  display: none;
  background-image: var(--addon-saving-red);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
}

.plDownloading{
  animation: spin 2s linear infinite;
}

.story-save-btn {
  background-image: var(--addon-save-yellow);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;

  cursor: pointer;
  width: 40px;
  height: 40px
}


.h-v-center {
  position: absolute;
  transform: translate(-50%, +60%);
  top: 50%;
  left: 50%;
}

.account-download-button {
  height: 30px;
  width: 30px;
  background: transparent;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;

  transform: translate(-50%, -50%);

  img {
    width: 100%;
    height: 100%;
  }
}

.hover-download-button {
  height: 30px;
  width: 30px;
  background: transparent;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;

  z-index: 5000000000000000000000000;

  img {
    width: 100%;
    height: 100%;
  }
}

.bulk-download-button {
  background-image: var(--extension-download-black);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  width: 30px;
  height: 30px;
  margin-right: -5px;
}


// Hover over a square image to display the download button
//account     explore        reels
._bz0w:hover, .pKKVh:hover, .Tjpra > a:hover {
  .hover-download-button {
    opacity: 1;
    visibility: visible;
  }
}


// Hover over account to display the download button
.RR-M-:hover, .M-jxE:hover, ._aarf._aarg:hover, ._aa_j ._aarf:hover {
  .account-download-button {
    opacity: 1;
    visibility: visible;
  }
}

// For the igtv tab
._bz0w {
  position: relative;

  // Hide the overlay if you download an image (igtv)
  &:active {
    opacity: 1 !important;
  }
}

// For the post download
.wmtNn, ._aamz > div {
  display: flex !important;
  flex-direction: row !important;
}

// Vertically the buttons around the bulk download button
._47KiJ, .XCodT, .r9-Os {
  align-items: center;
}


// V-center the icons in the top bar
.J5g42 {
  align-items: center;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.pldownload-button{
  display: inline-flex;
  align-items: center;
  height: 35px;
  padding: 5px 10px;
  border: 1px solid red;
  background-color: red;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
}

.udemy-post-save-btn-white {
  background-image: var(--addon-save-white);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.udemy-post-saving-btn-white {
  display: none;
  background-image: var(--addon-saving-white);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;
}

.udemy-pldownload-save-btn-white{
  background-image: var(--addon-save-white);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
  cursor: pointer;
  width: 28px;
  height: 28px;
  margin-right: 5px;
}

.udemy-pldownload-saving-btn-white {
  background-image: var(--addon-saving-white);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  width: 28px;
  height: 28px;
  margin-right: 5px;
  animation: spin 2s linear infinite;
}

.udemy-post-download-btn-div{
  display: inline-flex;
  align-items: center;
  margin: 5px;
  padding: 5px 10px;
  background-color: red;
  border-radius: 2px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
}

.udemy-discard-btn-white {
  background-image: var(--addon-discard-white);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.udemy-discard-btn-green {
  background-image: var(--addon-discard-green);
  background-size: 59%;
  background-repeat: no-repeat;
  background-position: center;
  display: block;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.noticTipP{
  display: inline-flex;
  align-items: center;
  margin: 20px 0 5px;
}

.noticButtonP{
  margin: 0;
  display: inline-flex;
  align-items: center;
}

.noticButtonP .noticA{
  background-color: red;
  cursor: pointer;
  text-decoration: none;
  display: block;
  color: #fff;
  padding: 5px 10px;
  border-radius: 5px;
  margin: 0 0 5px;
  display: inline-flex;
  align-items: center;
  height: 30px;
}

.notice_openSiderpanle{
  cursor: pointer;
  color: #5662F6;
}

.udemy-discard-div{
  font-size: 12px;
  background-color: #5662F6;
}

#pldownload-text{
  font-size: 1.6rem;
  font-weight: 700;
}