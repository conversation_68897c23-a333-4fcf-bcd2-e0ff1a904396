#dashdownloader
import os,requests,shutil,json,glob
from mpegdash.parser import MPEGDASHParser
from mpegdash.nodes import Descriptor
from mpegdash.utils import (
    parse_attr_value, parse_child_nodes, parse_node_value,
    write_attr_value, write_child_node, write_node_value
)

#global ids
quality = 1080 #default to 1080p
retry = 3
download_dir = os.getcwd() # set the folder to output
working_dir = os.getcwd() # set the folder to download ephemeral files
keyfile_path = working_dir + "/keyfile.json"

#Patching the Mpegdash lib for keyID
def __init__(self):
    self.scheme_id_uri = ''                               # xs:anyURI (required)
    self.value = None                                     # xs:string
    self.id = None                                        # xs:string
    self.key_id = None                                    # xs:string

def parse(self, xmlnode):
    self.scheme_id_uri = parse_attr_value(xmlnode, 'schemeIdUri', str)
    self.value = parse_attr_value(xmlnode, 'value', str)
    self.id = parse_attr_value(xmlnode, 'id', str)
    self.key_id = parse_attr_value(xmlnode, 'ns2:default_KID', str)
    if(self.key_id == None):
        self.key_id = parse_attr_value(xmlnode, 'cenc:default_KID', str)

def write(self, xmlnode):
    write_attr_value(xmlnode, 'schemeIdUri', self.scheme_id_uri)
    write_attr_value(xmlnode, 'value', self.value)
    write_attr_value(xmlnode, 'id', self.id)
    write_attr_value(xmlnode, 'ns2:default_KID', self.key_id)
    if(self.key_id == None):
        write_attr_value(xmlnode, 'cenc:default_KID', self.key_id)

Descriptor.__init__ = __init__
Descriptor.parse = parse
Descriptor.write = write

#Get the keys
with open(keyfile_path,'r') as keyfile:
    keyfile = keyfile.read()
keyfile = json.loads(keyfile)

def manifest_parser(mpd_url):
    print("جاري تحميل وتحليل ملف MPD...")

    # Headers مطلوبة للوصول لـ Udemy
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7,ar;q=0.6',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'DNT': '1',
        'Sec-CH-UA': '"Not?A_Brand";v="99", "Chromium";v="130"',
        'Sec-CH-UA-Mobile': '?0',
        'Sec-CH-UA-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-GPC': '1',
        'Referer': 'https://www.udemy.com/course/computermain/learn/lecture/30801830',
        'Origin': 'https://www.udemy.com'
    }

    # Cookies مهمة للمصادقة
    cookies = {
        '__udmy_2_v57r': 'e20fbe7e93b24085925001fbbe5600be',
        'ud_firstvisit': '2025-06-11T02:41:20.181668+00:00:1uPBOy:7nZt7LneoxAqNMHrWQkemnNjjejrG_yt-7Axrh1wzHQ',
        'ud_locale': 'en_US',
        'client_id': 'bd2565cb7b0c313f5e9bae44961e8db2',
        'access_token': 'PbKFeIsLuXfC0iDpB9cVHNTdF8RBV0+eXzo4ReTONb0:qzVXA8jeaLNzTlM+hwbwRIG1Y8NZ1+EQIsgQyLkoEp4',
        'ud_user_jwt': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************.TZpDeB28FEfmQ1kE8uqV6PNoscHD6n9Qtmg4GnRR6T0',
        'dj_session_id': '9eosv2sf2jeggrbg7a7opoanxkrogi1u',
        'csrftoken': '0IlSal5f4UJpJV10pozzdkOz9NudtZvc',
        'ud_cache_user': '198041138',
        'ud_cache_logged_in': '1',
        '__cf_bm': '0N2Scvcw8WyW.bbpMwMsfVosNdMP7xE8dRRvCwXoxXU-1753196321-1.0.1.1-NG_MOnKP3c_hgKRHoz6YMaujwZNDITM_TatcWCxGZZJHTh4TBlt6OgDUKGIUJpwkpnvfTD74cdxW57z57iLP.R3TedtKnXcla62IRxBjlyc',
        'cf_clearance': 'wYtazb3CIaP8_3oddD9rqkGVpWqXUAxqsBZZ6_Hdjz8-1753196321-1.2.1.1-qTougRYV5UVSqQKVSfVxyr7_TD88mNuGkBLxUcFj.e54cHrirEHPj46B.Pl8a56qvLeMmsWz90PfdJP.ZWD_v21Jw86zKMkSkfyROMSVanNfiV.D4Wa8ZOWqOzV1I3fiCdzowvF7z7Vl2vBCUOOjf2wdUaitZd5PhTXH8Y1AGZykjsMd63X1o05cvMwYM2KEzwl1.Pzz1Md70zXI_ChietngGTEzxoMnwuniAnzsePM'
    }

    manifest = requests.get(mpd_url, headers=headers, cookies=cookies).text
    with open("manifest.mpd",'w') as manifest_handler:
        manifest_handler.write(manifest)
    mpd = MPEGDASHParser.parse("./manifest.mpd")
    audio = []
    video = []

    for period in mpd.periods:
        for adapt_set in period.adaptation_sets:
            content_type = adapt_set.content_type
            print(f"معالجة {content_type}...")

            # البحث عن مفاتيح التشفير
            key_id = None
            for prot in adapt_set.content_protections:
                if prot.value == "cenc" and prot.key_id:
                    key_id = prot.key_id.replace('-','')
                    print(f"تم العثور على Key ID: {key_id}")
                    break

            for repr in adapt_set.representations:
                # التحقق من وجود base_urls
                if repr.base_urls and len(repr.base_urls) > 0:
                    base_url = repr.base_urls[0].base_url_value
                else:
                    # استخدام segment template إذا لم توجد base_urls
                    if repr.segment_template:
                        base_url = repr.segment_template.initialization
                    else:
                        print(f"تحذير: لم يتم العثور على URL للـ {content_type}")
                        continue

                if content_type == "audio":
                    audio.append(base_url)
                    if key_id:
                        audio.append(key_id)
                elif content_type == "video" and repr.height == quality:
                    video.append(base_url)
                    if key_id:
                        video.append(key_id)

    print(f"تم العثور على {len(video)//2} فيديو و {len(audio)//2} صوت")
    return video + audio

def download(url,filename,count = 0):
    video = requests.get(url, stream=True)
    if video.status_code == 200:
        video_length = int(video.headers.get('content-length'))
        if(os.path.isfile(filename) and os.path.getsize(filename) >= video_length):
            print("Video already downloaded.. skipping write to disk..")
        else:
            try:
                with open(filename, 'wb') as video_file:
                    shutil.copyfileobj(video.raw, video_file)
            except:
                print(url,filename)
                print("Write to disk error: Reattempting download and write..")
                if(count <= retry):
                    count += 1
                    download(url,filename,count)
                else:
                    exit("Error Writing Video to Disk. Exiting...")

        if os.path.getsize(filename) >= video_length:
            pass
        else:
            print("Error downloaded video is faulty.. Retrying to download")
            if(count <= retry):
                count += 1
                download(url,filename,count)
            else:
                exit("Error Writing Video to Disk. Exiting...")
    else:
        print("Video file is not accessible",filename,"Retrying...")
        if(count <= retry):
            count += 1
            download(url,filename,count)
        else:
            print("Adding Video file not accessible to log")
            with open(download_dir + "\video_access_error.txt",'a') as videoaccerr:
                videoaccerr.write(filename + " " + url +"\n")

def decrypt(filename,keyid,video_title):
    try:
        key = keyfile[keyid]
        print(key)
        os.system(f"ffmpeg -y -decryption_key {key} -i {filename} -codec copy -metadata title={video_title} dec_{filename}")
    except KeyError as error:
        print("Key not found")
        exit()

def mux_process(outfile):
    command = f"ffmpeg -y -i dec_audio.mp4 -i dec_video.mp4 -acodec copy -vcodec copy -metadata title=\"{video_title}\" {outfile}.mp4"
    print(command)
    os.system(command)

def cleanup(path):
    leftover_files = glob.glob(path + '/*.mp4', recursive=True)
    mpd_files = glob.glob(path + '/*.mpd', recursive=True)
    leftover_files = leftover_files + mpd_files
    for file_list in leftover_files:
        try:
            os.remove(file_list)
        except OSError:
            print(f"Error deleting file: {file_list}")


if __name__ == "__main__":
    # رابط MPD من Udemy
    mpd = "https://www.udemy.com/assets/39324048/encrypted-files/out/v1/ab6818eaa27c464ab8afce6ecf1f499b/3e691b0da4414b83836363226ac161e9/ecd8798041b7474bb05daed9aae97b93/index.mpd?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.B8LCCPJcX1_I3BqL6Qs1DygWhIijPhJ6KS_V-4WYniY&provider=cloudfront&v=1&CMCD=ot%3Dm%2Csf%3Dd%2Csid%3D%223e12a4bc-1645-48eb-98e4-5f772936d8c4%22%2Csu"

    # استخراج الـ base URL
    base_url = mpd.split("index.mpd")[0]

    os.chdir(working_dir)
    video_url,video_keyid,audio_url,audio_keyid = manifest_parser(mpd)
    video_url = base_url + video_url
    audio_url = base_url + audio_url
    audio_filename = "audio.mp4"
    video_filename = "video.mp4"
    video_title = "Udemy_Course_Video"  # غير هذا العنوان حسب المحتوى
    audio_title = video_title

    print(f"تحميل الفيديو من: {video_url}")
    print(f"تحميل الصوت من: {audio_url}")
    print(f"Video Key ID: {video_keyid}")
    print(f"Audio Key ID: {audio_keyid}")

    download(video_url,video_filename)
    download(audio_url,audio_filename)
    decrypt(video_filename,video_keyid,video_title)
    decrypt(audio_filename,audio_keyid,audio_title)
    final_file = download_dir + '/' + video_title
    print(final_file)
    mux_process(final_file)
    cleanup(working_dir)
